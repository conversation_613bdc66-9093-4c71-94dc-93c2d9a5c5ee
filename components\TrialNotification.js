function TrialNotification({ onActivate, onContinue }) {
    try {
        const [showDetails, setShowDetails] = React.useState(false);
        const activationStatus = getActivationStatus();

        return (
            <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4 z-40" data-name="trial-notification" data-file="components/TrialNotification.js">
                <div className="glass-effect rounded-2xl p-6 w-full max-w-lg">
                    <div className="text-center mb-6">
                        <i className="fas fa-clock text-4xl text-orange-400 mb-4"></i>
                        <h2 className="text-2xl font-bold mb-2">Демоверсия Password Manager</h2>
                        <p className="text-gray-400">
                            Вы используете пробную версию программы
                        </p>
                    </div>

                    <div className="bg-orange-900 bg-opacity-50 rounded-lg p-4 mb-6">
                        <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center">
                                <i className="fas fa-info-circle text-orange-400 mr-2"></i>
                                <span className="font-semibold text-orange-400">Статус демоверсии</span>
                            </div>
                            <button
                                onClick={() => setShowDetails(!showDetails)}
                                className="text-orange-400 hover:text-orange-300 transition-colors"
                            >
                                <i className={`fas fa-chevron-${showDetails ? 'up' : 'down'}`}></i>
                            </button>
                        </div>
                        
                        <div className="space-y-2">
                            <div className="flex justify-between">
                                <span className="text-sm text-gray-300">Текущий запуск:</span>
                                <span className="text-sm font-medium text-orange-400">
                                    {activationStatus.trialCount} из {activationStatus.maxTrialRuns}
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm text-gray-300">Осталось запусков:</span>
                                <span className="text-sm font-medium text-orange-400">
                                    {activationStatus.remainingRuns}
                                </span>
                            </div>
                            
                            {/* Прогресс-бар */}
                            <div className="mt-3">
                                <div className="w-full bg-gray-700 rounded-full h-2">
                                    <div 
                                        className="bg-orange-400 h-2 rounded-full transition-all duration-300"
                                        style={{ 
                                            width: `${(activationStatus.trialCount / activationStatus.maxTrialRuns) * 100}%` 
                                        }}
                                    ></div>
                                </div>
                            </div>
                        </div>

                        {showDetails && (
                            <div className="mt-4 pt-4 border-t border-orange-700">
                                <div className="space-y-2 text-sm">
                                    {activationStatus.firstRunDate && (
                                        <div className="flex justify-between">
                                            <span className="text-gray-300">Первый запуск:</span>
                                            <span className="text-gray-300">
                                                {activationStatus.firstRunDate.toLocaleDateString('ru-RU')}
                                            </span>
                                        </div>
                                    )}
                                    <div className="text-gray-400 text-xs mt-2">
                                        После {activationStatus.maxTrialRuns} запусков потребуется активация для продолжения работы.
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    <div className="space-y-4">
                        <div className="bg-blue-900 bg-opacity-30 rounded-lg p-4">
                            <h3 className="font-semibold text-blue-400 mb-2">
                                <i className="fas fa-star mr-2"></i>
                                Преимущества полной версии
                            </h3>
                            <ul className="text-sm text-gray-300 space-y-1">
                                <li>• Неограниченное количество запусков</li>
                                <li>• Полный доступ ко всем функциям</li>
                                <li>• Техническая поддержка</li>
                                <li>• Регулярные обновления</li>
                            </ul>
                        </div>

                        <div className="flex space-x-3">
                            <button
                                onClick={onContinue}
                                className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold transition-colors"
                            >
                                Продолжить ({activationStatus.remainingRuns} запусков)
                            </button>
                            <button
                                onClick={onActivate}
                                className="flex-1 py-3 btn-primary rounded-lg font-semibold transition-colors"
                            >
                                <i className="fas fa-key mr-2"></i>
                                Активировать
                            </button>
                        </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-gray-700 text-center">
                        <button
                            onClick={() => window.open('https://vk.com/neirotophcik', '_blank')}
                            className="text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors"
                        >
                            <i className="fab fa-vk mr-1"></i>
                            Получить ключ активации
                        </button>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('TrialNotification component error:', error);
        if (typeof reportError === 'function') {
            reportError(error);
        }
        return null;
    }
}
