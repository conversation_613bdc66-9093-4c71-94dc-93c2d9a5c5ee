function TableViewer({ table, onClose }) {
    const [copiedCell, setCopiedCell] = React.useState(null);

    const copyToClipboard = async (text, cellId) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopiedCell(cellId);
            showToast('Текст скопирован в буфер обмена');
            
            // Убираем индикатор через 2 секунды
            setTimeout(() => setCopiedCell(null), 2000);
        } catch (error) {
            console.error('Ошибка копирования:', error);
            showToast('Ошибка копирования текста', 'error');
        }
    };

    const copyTableAsText = () => {
        let tableText = '';
        
        // Добавляем заголовки
        tableText += table.headers.join('\t') + '\n';
        
        // Добавляем данные
        table.data.forEach(row => {
            tableText += row.join('\t') + '\n';
        });
        
        copyToClipboard(tableText, 'full-table');
    };

    const copyTableAsCSV = () => {
        let csvText = '';
        
        // Добавляем заголовки
        csvText += table.headers.map(header => `"${header}"`).join(',') + '\n';
        
        // Добавляем данные
        table.data.forEach(row => {
            csvText += row.map(cell => `"${cell}"`).join(',') + '\n';
        });
        
        copyToClipboard(csvText, 'csv-table');
    };

    const formatDate = (dateString) => {
        if (!dateString) return 'Неизвестно';
        try {
            return new Date(dateString).toLocaleDateString('ru-RU', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch {
            return 'Неизвестно';
        }
    };

    return (
        <div className="fixed inset-0 bg-gray-900 z-50">
            <header className="bg-gray-800 border-b border-gray-700 px-6 py-4">
                <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-3">
                        <i className="fas fa-eye text-2xl text-blue-400"></i>
                        <div>
                            <h1 className="text-xl font-bold text-white">{table.name}</h1>
                            <p className="text-sm text-gray-400">
                                {table.data?.length || 0} строк, {table.headers?.length || 0} колонок
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center space-x-3">
                        <button
                            onClick={copyTableAsText}
                            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
                            title="Копировать как текст (разделители - табуляция)"
                        >
                            <i className="fas fa-copy mr-2"></i>Копировать текст
                        </button>
                        <button
                            onClick={copyTableAsCSV}
                            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
                            title="Копировать как CSV"
                        >
                            <i className="fas fa-file-csv mr-2"></i>Копировать CSV
                        </button>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-white transition-colors"
                            title="Закрыть"
                        >
                            <i className="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </header>

            <div className="p-6 h-full overflow-auto">
                <div className="max-w-full mx-auto">
                    {/* Информация о таблице */}
                    <div className="mb-6 p-4 bg-gray-800 border border-gray-700 rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span className="text-gray-400">Создано:</span>
                                <span className="ml-2 text-white">{formatDate(table.createdAt)}</span>
                            </div>
                            {table.updatedAt && table.updatedAt !== table.createdAt && (
                                <div>
                                    <span className="text-gray-400">Изменено:</span>
                                    <span className="ml-2 text-white">{formatDate(table.updatedAt)}</span>
                                </div>
                            )}
                            <div>
                                <span className="text-gray-400">Размер:</span>
                                <span className="ml-2 text-white">
                                    {table.data?.length || 0} × {table.headers?.length || 0}
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* Полная таблица */}
                    {table.headers && table.data && (
                        <div className="overflow-x-auto bg-gray-800 border border-gray-700 rounded-lg">
                            <table className="w-full border-collapse">
                                <thead>
                                    <tr>
                                        <th className="w-12 p-3 bg-gray-700 border border-gray-600 text-center text-gray-400 font-medium">
                                            #
                                        </th>
                                        {table.headers.map((header, index) => (
                                            <th
                                                key={index}
                                                className="p-3 bg-gray-700 border border-gray-600 text-left font-medium cursor-pointer hover:bg-gray-600 transition-colors"
                                                style={{ color: table.headerColors?.[index] || '#d1d5db' }}
                                                onClick={() => copyToClipboard(header, `header-${index}`)}
                                                title="Нажмите, чтобы скопировать заголовок"
                                            >
                                                <div className="flex items-center justify-between">
                                                    <span>{header || `Колонка ${index + 1}`}</span>
                                                    {copiedCell === `header-${index}` && (
                                                        <i className="fas fa-check text-green-400 ml-2"></i>
                                                    )}
                                                </div>
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody>
                                    {table.data.map((row, rowIndex) => (
                                        <tr key={rowIndex} className="hover:bg-gray-750 transition-colors">
                                            <td className="p-3 bg-gray-800 border border-gray-600 text-center text-gray-400 font-medium">
                                                {rowIndex + 1}
                                            </td>
                                            {row.map((cell, colIndex) => (
                                                <td
                                                    key={colIndex}
                                                    className="p-3 border border-gray-600 text-gray-300 cursor-pointer hover:bg-gray-700 transition-colors"
                                                    onClick={() => copyToClipboard(cell, `cell-${rowIndex}-${colIndex}`)}
                                                    title="Нажмите, чтобы скопировать ячейку"
                                                >
                                                    <div className="flex items-center justify-between">
                                                        <span className="break-words">{cell || '-'}</span>
                                                        {copiedCell === `cell-${rowIndex}-${colIndex}` && (
                                                            <i className="fas fa-check text-green-400 ml-2"></i>
                                                        )}
                                                    </div>
                                                </td>
                                            ))}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}

                    {/* Инструкции */}
                    <div className="mt-6 p-4 bg-blue-900 bg-opacity-30 border border-blue-600 rounded-lg">
                        <h3 className="text-blue-300 font-semibold mb-2">
                            <i className="fas fa-info-circle mr-2"></i>Как копировать данные:
                        </h3>
                        <ul className="text-blue-200 text-sm space-y-1">
                            <li>• <strong>Отдельная ячейка:</strong> Нажмите на любую ячейку</li>
                            <li>• <strong>Заголовок:</strong> Нажмите на заголовок колонки</li>
                            <li>• <strong>Вся таблица:</strong> Используйте кнопки "Копировать текст" или "Копировать CSV"</li>
                            <li>• <strong>Формат текста:</strong> Колонки разделены табуляцией</li>
                            <li>• <strong>Формат CSV:</strong> Колонки разделены запятыми, значения в кавычках</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );
}
