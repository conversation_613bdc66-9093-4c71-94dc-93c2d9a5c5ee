function DocumentsPage({ onClose }) {
    try {
        const [documents, setDocuments] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [showAddForm, setShowAddForm] = React.useState(false);
        const [showEditForm, setShowEditForm] = React.useState(false);
        const [editingDocument, setEditingDocument] = React.useState(null);
        
        const loadDocuments = async () => {
            try {
                setLoading(true);
                const docs = await getDocuments();
                setDocuments(docs);
            } catch (error) {
                console.error('Error loading documents:', error);
                showToast('Ошибка при загрузке документов', 'error');
            } finally {
                setLoading(false);
            }
        };
        
        React.useEffect(() => {
            loadDocuments();
        }, []);
        
        const handleAddDocument = () => {
            setShowAddForm(true);
        };
        
        const handleDocumentAdded = () => {
            setShowAddForm(false);
            loadDocuments();
        };
        
        const handleEditDocument = (document) => {
            setEditingDocument(document);
            setShowEditForm(true);
        };
        
        const handleDocumentEdited = () => {
            setShowEditForm(false);
            setEditingDocument(null);
            loadDocuments();
        };
        
        const handleDeleteDocument = async (id) => {
            try {
                await deleteDocument(id);
                showToast('Документ удален');
                loadDocuments();
            } catch (error) {
                console.error('Error deleting document:', error);
                showToast('Ошибка при удалении документа', 'error');
            }
        };
        
        if (loading) {
            return (
                <div className="fixed inset-0 bg-gray-900 z-50">
                    <div className="flex items-center justify-center h-full">
                        <i className="fas fa-spinner fa-spin text-4xl text-blue-400"></i>
                    </div>
                </div>
            );
        }
        
        return (
            <div className="fixed inset-0 bg-gray-900 z-50 overflow-y-auto">
                <header className="glass-effect border-b border-gray-700 px-6 py-4">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                            <i className="fas fa-id-card text-2xl text-blue-400"></i>
                            <h1 className="text-xl font-bold">Документы</h1>
                        </div>
                        <div className="flex items-center space-x-4">
                            <button
                                onClick={handleAddDocument}
                                className="text-gray-400 hover:text-white transition-colors"
                                title="Добавить документ"
                            >
                                <i className="fas fa-plus"></i>
                            </button>
                            <button
                                onClick={onClose}
                                className="text-gray-400 hover:text-white transition-colors"
                                title="Вернуться"
                            >
                                <i className="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </header>

                <main className="p-6">
                    <div className="max-w-6xl mx-auto">
                        {documents.length === 0 ? (
                            <div className="text-center py-10">
                                <i className="fas fa-file-alt text-4xl text-gray-600 mb-3"></i>
                                <p className="text-gray-400">У вас пока нет сохраненных документов</p>
                                <button
                                    onClick={handleAddDocument}
                                    className="mt-4 px-4 py-2 btn-primary rounded-lg"
                                >
                                    Добавить первый документ
                                </button>
                            </div>
                        ) : (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {documents.map(doc => (
                                    <DocumentView
                                        key={doc.id}
                                        document={doc}
                                        onDelete={handleDeleteDocument}
                                        onEdit={handleEditDocument}
                                    />
                                ))}
                            </div>
                        )}
                    </div>
                </main>
                
                {showAddForm && (
                    <DocumentForm
                        onAdd={handleDocumentAdded}
                        onCancel={() => setShowAddForm(false)}
                    />
                )}
                
                {showEditForm && editingDocument && (
                    <DocumentEditForm
                        document={editingDocument}
                        onSave={handleDocumentEdited}
                        onCancel={() => {
                            setShowEditForm(false);
                            setEditingDocument(null);
                        }}
                    />
                )}
            </div>
        );
    } catch (error) {
        console.error('DocumentsPage component error:', error);
        reportError(error);
    }
}