# Руководство по тестированию системы активации

## Описание системы

Система активации включает в себя:
- **К<PERSON>юч активации**: `neirotopchik_vk_2000_500-7000!!!!!`
- **Демоверсия**: 10 бесплатных запусков
- **Уведомления**: Показываются при каждом запуске демоверсии
- **Принудительная активация**: После 10 запусков

## Как тестировать

### 1. Первый запуск (демоверсия)
- Откройте приложение в браузере
- Должно появиться уведомление о демоверсии
- Показывается "Запуск 1 из 10"
- Можно нажать "Продолжить" или "Активировать"

### 2. Тестирование счетчика запусков
В консоли браузера (F12) выполните:
```javascript
// Посмотреть текущий статус
debugActivation()

// Сбросить данные активации для повторного тестирования
resetActivation()

// Перезагрузить страницу
location.reload()
```

### 3. Тестирование активации
- Нажмите кнопку "Активировать" в уведомлении
- Введите ключ: `neirotopchik_vk_2000_500-7000!!!!!`
- Нажмите "Активировать"
- Должно появиться сообщение об успешной активации

### 4. Тестирование истечения пробного периода
Для быстрого тестирования:
```javascript
// Установить счетчик на 10 (максимум)
localStorage.setItem('passwordManager_trialCount', '10')
location.reload()
```

После этого должно появиться окно принудительной активации.

### 5. Проверка активированной версии
После активации:
- Уведомления о демоверсии не показываются
- Приложение работает в обычном режиме
- Статус активации сохраняется между сессиями

## Функции отладки

В консоли браузера доступны:
- `debugActivation()` - показать текущий статус
- `resetActivation()` - сбросить все данные активации

## Структура данных в localStorage

- `passwordManager_activated` - статус активации (true/false)
- `passwordManager_trialCount` - количество запусков демоверсии
- `passwordManager_firstRunDate` - дата первого запуска

## Возможные проблемы

1. **Уведомление не показывается**: Проверьте, что утилита activation.js загружена
2. **Активация не работает**: Убедитесь, что ключ введен точно
3. **Счетчик не увеличивается**: Проверьте консоль на ошибки JavaScript

## Сброс для повторного тестирования

Для полного сброса выполните в консоли:
```javascript
localStorage.clear()
location.reload()
```
