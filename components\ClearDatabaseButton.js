function ClearDatabaseButton() {
    const [isClearing, setIsClearing] = React.useState(false);
    
    const clearDatabase = async () => {
        const confirmMessage = `⚠️ ВНИМАНИЕ: Очистка базы данных

Будет удалено:
• Все пароли и карты
• Все заметки и документы
• КЛЮЧ ЛИЦЕНЗИИ (потребуется заново вводить ключ активации)

Это действие НЕВОЗМОЖНО отменить!

Продолжить?`;

        if (confirm(confirmMessage)) {
            setIsClearing(true);
            try {
                // Удаляем базу данных
                await new Promise((resolve, reject) => {
                    const request = indexedDB.deleteDatabase('passwordManagerDB');
                    request.onsuccess = () => {
                        console.log('База данных успешно удалена');

                        // Сбрасываем данные активации
                        localStorage.clear();
                        localStorage.setItem('passwordManager_trialCount', '0');
                        console.log('🔢 Trial count reset to 0 for next login');

                        resolve();
                    };
                    request.onerror = (event) => {
                        console.error('Ошибка при удалении базы данных:', event.target.error);
                        reject(event.target.error);
                    };
                });
                
                showToast('База данных очищена. Страница будет перезагружена.');
                
                // Перезагружаем страницу через 2 секунды
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } catch (error) {
                console.error('Ошибка при очистке базы данных:', error);
                showToast('Ошибка при очистке базы данных', 'error');
                setIsClearing(false);
            }
        }
    };
    
    return (
        <button
            onClick={clearDatabase}
            disabled={isClearing}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
        >
            {isClearing ? 'Очистка...' : 'Очистить базу данных'}
        </button>
    );
}