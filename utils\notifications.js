// Утилиты для отображения уведомлений
const showToast = (message, type = 'success') => {
    try {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${
            type === 'success' ? 'bg-green-600' : 'bg-red-600'
        }`;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    } catch (error) {
        console.error('Show toast error:', error);
    }
};

// Экспортируем функцию в глобальную область видимости
window.showToast = showToast;