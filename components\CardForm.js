function CardForm({ onAdd, onCancel }) {
    try {
        const [formData, setFormData] = React.useState({
            title: '',
            cardNumber: '',
            cvv: '',
            expiryMonth: '',
            expiryYear: '',
            firstName: '',
            lastName: '',
            notes: ''
        });
        
        const [showCardNumber, setShowCardNumber] = React.useState(false);
        const [showCVV, setShowCVV] = React.useState(false);

        const handleSubmit = async (e) => {
            e.preventDefault();
            
            // Минимальная проверка только на наличие заголовка
            if (!formData.title) {
                showToast('Please enter card title', 'error');
                return;
            }

            try {
                await saveCard(formData);
                showToast('Card saved successfully');
                onAdd();
            } catch (error) {
                showToast('Failed to save card', 'error');
            }
        };

        const handleCardNumberChange = (e) => {
            // Разрешаем вводить только цифры
            const numericValue = e.target.value.replace(/\D/g, '');
            // Форматируем номер карты с пробелами после каждых 4 цифр
            const formatted = numericValue.replace(/(\d{4})(?=\d)/g, '$1 ');
            setFormData({ ...formData, cardNumber: formatted });
        };

        return (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50" data-name="card-form-modal" data-file="components/CardForm.js">
                <div className="glass-effect rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-xl font-bold">Add Bank Card</h2>
                        <button onClick={onCancel} className="text-gray-400 hover:text-white">
                            <i className="fas fa-times"></i>
                        </button>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <input
                            type="text"
                            placeholder="Card Title *"
                            value={formData.title}
                            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                            className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                            required
                        />

                        <div className="relative">
                            <input
                                type={showCardNumber ? "text" : "password"}
                                placeholder="Card Number *"
                                value={formData.cardNumber}
                                onChange={handleCardNumberChange}
                                className="w-full px-4 py-3 pr-10 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none font-mono"
                                required
                            />
                            <button
                                type="button"
                                onClick={() => setShowCardNumber(!showCardNumber)}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                            >
                                <i className={`fas ${showCardNumber ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                            </button>
                        </div>

                        <div className="relative">
                            <input
                                type={showCVV ? "text" : "password"}
                                placeholder="CVV *"
                                value={formData.cvv}
                                onChange={(e) => setFormData({ ...formData, cvv: e.target.value.replace(/\D/g, '') })}
                                className="w-full px-4 py-3 pr-10 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                required
                            />
                            <button
                                type="button"
                                onClick={() => setShowCVV(!showCVV)}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                            >
                                <i className={`fas ${showCVV ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                            </button>
                        </div>

                        <div className="flex gap-3">
                            <input
                                type="text"
                                placeholder="Month (MM) *"
                                value={formData.expiryMonth}
                                onChange={(e) => setFormData({ ...formData, expiryMonth: e.target.value.replace(/\D/g, '') })}
                                className="flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                required
                            />
                            <input
                                type="text"
                                placeholder="Year (YYYY) *"
                                value={formData.expiryYear}
                                onChange={(e) => setFormData({ ...formData, expiryYear: e.target.value.replace(/\D/g, '') })}
                                className="flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                required
                            />
                        </div>

                        <div className="flex gap-3">
                            <input
                                type="text"
                                placeholder="First Name *"
                                value={formData.firstName}
                                onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                                className="flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                required
                            />
                            <input
                                type="text"
                                placeholder="Last Name *"
                                value={formData.lastName}
                                onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                                className="flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                required
                            />
                        </div>

                        <textarea
                            placeholder="Additional Notes (Optional)"
                            value={formData.notes}
                            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                            className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none resize-none"
                            rows="3"
                        ></textarea>

                        <div className="flex space-x-3 pt-4">
                            <button
                                type="submit"
                                className="flex-1 py-3 btn-primary rounded-lg font-semibold"
                            >
                                Save Card
                            </button>
                            <button
                                type="button"
                                onClick={onCancel}
                                className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold transition-colors"
                            >
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    } catch (error) {
        console.error('CardForm component error:', error);
        reportError(error);
    }
}
