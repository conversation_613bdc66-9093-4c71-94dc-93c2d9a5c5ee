function PasswordCard({ password, onDelete, onEdit }) {
    try {
        const [showPassword, setShowPassword] = React.useState(false);
        const [showImageViewer, setShowImageViewer] = React.useState(false);

        const handleCopy = async (text, field) => {
            const success = await copyToClipboard(text);
            if (success) {
                showToast(`${field} copied to clipboard`);
            }
        };

        const handleDelete = async () => {
            if (confirm('Are you sure you want to delete this password?')) {
                try {
                    await onDelete(password.id);
                } catch (error) {
                    console.error('Delete error:', error);
                    showToast('Failed to delete password', 'error');
                }
            }
        };

        return (
            <div 
                className="glass-effect rounded-xl p-6 card-hover" 
                style={{ backgroundColor: password.cardBgColor || 'transparent' }}
                data-name="password-card" 
                data-file="components/PasswordCard.js"
            >
                <div className="flex justify-between items-start mb-4">
                    <h3 className="text-lg font-semibold" style={{ color: password.titleColor || password.color || '#ffffff', fontFamily: password.titleFont || 'sans-serif' }}>{password.title || 'Untitled'}</h3>
                    <div className="flex space-x-2">
                        <button
                            onClick={() => onEdit(password)}
                            className="text-blue-400 hover:text-blue-300 transition-colors"
                            type="button"
                        >
                            <i className="fas fa-edit"></i>
                        </button>
                        <button
                            onClick={handleDelete}
                            className="text-red-400 hover:text-red-300 transition-colors"
                            type="button"
                        >
                            <i className="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                {password.imageUrl && (
                    <div className="mb-4">
                        <img 
                            src={password.imageUrl} 
                            alt="Card Image" 
                            className="w-full h-32 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                            onClick={() => setShowImageViewer(true)}
                        />
                    </div>
                )}

                <div className="space-y-3">
                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <label className="text-sm text-gray-400">Email</label>
                            <p className="truncate" style={{ color: password.emailColor || password.color || '#ffffff', fontFamily: password.emailFont || 'sans-serif' }}>{password.email}</p>
                        </div>
                        <button
                            onClick={() => handleCopy(password.email, 'Email')}
                            className="ml-2 text-blue-400 hover:text-blue-300"
                            type="button"
                        >
                            <i className="fas fa-copy"></i>
                        </button>
                    </div>
                    
                    {password.url && (
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <label className="text-sm text-gray-400">Website</label>
                                <a 
                                    href={password.url.startsWith('http') ? password.url : `https://${password.url}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-400 hover:text-blue-300 truncate block"
                                    style={{ fontFamily: password.urlFont || 'sans-serif' }}
                                >
                                    {password.url}
                                </a>
                            </div>
                            <button
                                onClick={() => handleCopy(password.url, 'URL')}
                                className="ml-2 text-blue-400 hover:text-blue-300"
                                type="button"
                            >
                                <i className="fas fa-copy"></i>
                            </button>
                        </div>
                    )}
                    
                    {password.phone && (
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <label className="text-sm text-gray-400">Phone</label>
                                <p className="truncate" style={{ color: password.phoneColor || password.color || '#ffffff', fontFamily: password.phoneFont || 'sans-serif' }}>{password.phone}</p>
                            </div>
                            <button
                                onClick={() => handleCopy(password.phone, 'Phone')}
                                className="ml-2 text-blue-400 hover:text-blue-300"
                                type="button"
                            >
                                <i className="fas fa-copy"></i>
                            </button>
                        </div>
                    )}

                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <label className="text-sm text-gray-400">Username</label>
                            <p className="truncate" style={{ color: password.usernameColor || password.color || '#ffffff', fontFamily: password.usernameFont || 'sans-serif' }}>{password.username}</p>
                        </div>
                        <button
                            onClick={() => handleCopy(password.username, 'Username')}
                            className="ml-2 text-blue-400 hover:text-blue-300"
                            type="button"
                        >
                            <i className="fas fa-copy"></i>
                        </button>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <label className="text-sm text-gray-400">Password</label>
                            <p 
                                className="font-semibold"
                                style={{ 
                                    color: password.passwordColor || password.color || '#ffffff',
                                    fontFamily: password.passwordFont || 'monospace'
                                }}
                            >
                                {showPassword ? password.password : '••••••••'}
                            </p>
                        </div>
                        <div className="flex space-x-2">
                            <button
                                onClick={() => setShowPassword(!showPassword)}
                                className="text-gray-400 hover:text-gray-300"
                                type="button"
                            >
                                <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                            </button>
                            <button
                                onClick={() => handleCopy(password.password, 'Password')}
                                className="text-blue-400 hover:text-blue-300"
                                type="button"
                            >
                                <i className="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    {password.notes && (
                        <div>
                            <label className="text-sm text-gray-400">Notes</label>
                            <p className="text-sm" style={{ color: password.notesColor || password.color || '#ffffff', fontFamily: password.notesFont || 'sans-serif' }}>{password.notes}</p>
                        </div>
                    )}

                    <div className="text-xs text-gray-500 mt-2">
                        <span>Category: {password.category || 'Uncategorized'}</span>
                    </div>
                </div>

                {showImageViewer && password.imageUrl && (
                    <ImageViewer 
                        imageUrl={password.imageUrl} 
                        onClose={() => setShowImageViewer(false)} 
                    />
                )}
            </div>
        );
    } catch (error) {
        console.error('PasswordCard component error:', error);
        reportError(error);
    }
}