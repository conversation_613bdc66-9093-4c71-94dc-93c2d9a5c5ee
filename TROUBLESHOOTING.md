# Руководство по устранению неполадок

## Проблема: "Ошибка инициализации" или "Неверный пароль"

### Диагностика проблемы

1. **Откройте консоль браузера** (F12 → Console)

2. **Проверьте состояние хранилища:**
```javascript
checkStorageState()
```

3. **Проверьте активацию:**
```javascript
debugActivation()
```

### Возможные решения

#### Решение 1: Сброс приложения (если ничего не помогает)
```javascript
resetApp()
```
⚠️ **ВНИМАНИЕ**: Это удалит ВСЕ данные!

#### Решение 2: Тестирование конкретного пароля
```javascript
testPassword("ваш_пароль")
```

#### Решение 3: Ручное исправление базы данных
```javascript
// Если пароль есть в localStorage, но не работает в IndexedDB
localStorage.setItem('needDbReset', 'true')
location.reload()
```

#### Решение 4: Сброс только активации
```javascript
resetActivation()
location.reload()
```

### Частые проблемы и решения

#### 1. Приложение не загружается
- Проверьте консоль на ошибки JavaScript
- Убедитесь, что все файлы загружены
- Попробуйте очистить кеш браузера (Ctrl+F5)

#### 2. "Неверный пароль" при правильном пароле
- Выполните `checkStorageState()` в консоли
- Проверьте, есть ли пароль в localStorage и IndexedDB
- Попробуйте `testPassword("ваш_пароль")`

#### 3. Проблемы с активацией
- Выполните `debugActivation()` в консоли
- Проверьте правильность ключа: `neirotopchik_vk_2000_500-7000!!!!!`
- При необходимости сбросьте: `resetActivation()`

#### 4. База данных не инициализируется
- Проверьте поддержку IndexedDB в браузере
- Попробуйте в режиме инкогнито
- Очистите данные сайта в настройках браузера

### Пошаговая диагностика

1. **Откройте консоль** (F12)
2. **Выполните команды по порядку:**

```javascript
// Шаг 1: Проверка общего состояния
checkStorageState()

// Шаг 2: Проверка активации
debugActivation()

// Шаг 3: Если есть проблемы с паролем
testPassword("ваш_пароль_здесь")

// Шаг 4: Если ничего не помогает
resetApp()
```

### Логи для отправки разработчику

Если проблема не решается, выполните в консоли:
```javascript
console.log('=== DEBUG INFO ===')
checkStorageState()
debugActivation()
console.log('User Agent:', navigator.userAgent)
console.log('==================')
```

Скопируйте весь вывод и отправьте разработчику.

### Контакты для поддержки

- VK: https://vk.com/neirotophcik
- При обращении приложите логи из консоли
