// Утилита для безопасной очистки localStorage с сохранением данных активации

/**
 * Безопасно очищает localStorage, сохраняя данные активации
 */
function safeClearLocalStorage() {
    console.log('🧹 Starting safe localStorage clear...');
    
    // Сохраняем данные активации
    const activationData = {
        activated: localStorage.getItem('passwordManager_activated'),
        trialCount: localStorage.getItem('passwordManager_trialCount'),
        firstRunDate: localStorage.getItem('passwordManager_firstRunDate')
    };
    
    console.log('💾 Saved activation data:', {
        activated: !!activationData.activated,
        trialCount: activationData.trialCount,
        firstRunDate: !!activationData.firstRunDate
    });
    
    // Очищаем localStorage полностью
    localStorage.clear();
    console.log('🗑️ localStorage cleared');
    
    // Восстанавливаем данные активации
    if (activationData.activated) {
        localStorage.setItem('passwordManager_activated', activationData.activated);
        console.log('✅ Restored activation status');
    }
    if (activationData.trialCount) {
        localStorage.setItem('passwordManager_trialCount', activationData.trialCount);
        console.log('✅ Restored trial count');
    }
    if (activationData.firstRunDate) {
        localStorage.setItem('passwordManager_firstRunDate', activationData.firstRunDate);
        console.log('✅ Restored first run date');
    }
    
    console.log('🔒 Activation data preserved during localStorage clear');
}

/**
 * Полностью очищает localStorage (включая данные активации) - только для отладки
 */
function unsafeClearLocalStorage() {
    console.warn('⚠️ UNSAFE: Clearing ALL localStorage data including activation');
    localStorage.clear();
}

/**
 * Показывает текущие данные активации в localStorage
 */
function showActivationData() {
    console.log('📊 Current activation data in localStorage:');
    console.log('- Activated:', localStorage.getItem('passwordManager_activated'));
    console.log('- Trial count:', localStorage.getItem('passwordManager_trialCount'));
    console.log('- First run date:', localStorage.getItem('passwordManager_firstRunDate'));
}

// Делаем функции глобально доступными
window.safeClearLocalStorage = safeClearLocalStorage;
window.unsafeClearLocalStorage = unsafeClearLocalStorage;
window.showActivationData = showActivationData;
