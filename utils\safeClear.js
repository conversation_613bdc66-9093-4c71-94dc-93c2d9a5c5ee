// Утилита для безопасной очистки localStorage с сохранением данных активации

/**
 * Безопасно очищает localStorage, сохраняя данные активации
 */
function safeClearLocalStorage() {
    console.log('🧹 Starting safe localStorage clear...');
    
    // Сохраняем данные активации
    const activationData = {
        activated: localStorage.getItem('passwordManager_activated'),
        trialCount: localStorage.getItem('passwordManager_trialCount'),
        firstRunDate: localStorage.getItem('passwordManager_firstRunDate')
    };
    
    console.log('💾 Saved activation data:', {
        activated: !!activationData.activated,
        trialCount: activationData.trialCount,
        firstRunDate: !!activationData.firstRunDate
    });
    
    // Очищаем localStorage полностью
    localStorage.clear();
    console.log('🗑️ localStorage cleared');
    
    // Восстанавливаем данные активации
    if (activationData.activated) {
        localStorage.setItem('passwordManager_activated', activationData.activated);
        console.log('✅ Restored activation status');
    }
    if (activationData.trialCount) {
        localStorage.setItem('passwordManager_trialCount', activationData.trialCount);
        console.log('✅ Restored trial count');
    }
    if (activationData.firstRunDate) {
        localStorage.setItem('passwordManager_firstRunDate', activationData.firstRunDate);
        console.log('✅ Restored first run date');
    }
    
    console.log('🔒 Activation data preserved during localStorage clear');
}

/**
 * Полностью очищает localStorage включая ВСЕ данные активации
 * Используется при очистке базы данных - полный сброс
 */
function clearAllIncludingActivation() {
    console.log('🔄 FULL CLEAR: Clearing ALL localStorage data including activation');
    console.log('📊 Before clear:');
    console.log('- Activated:', localStorage.getItem('passwordManager_activated'));
    console.log('- Trial count:', localStorage.getItem('passwordManager_trialCount'));
    console.log('- First run date:', localStorage.getItem('passwordManager_firstRunDate'));

    localStorage.clear();

    // Принудительно устанавливаем счетчик в 0 для следующего входа
    localStorage.setItem('passwordManager_trialCount', '0');

    console.log('🗑️ ALL localStorage data cleared');
    console.log('🔢 Trial count explicitly set to 0');
    console.log('🔄 User will start fresh with trial count 0');
    console.log('🔑 User will need to activate from beginning');
}

/**
 * Полностью очищает localStorage (включая данные активации) - только для отладки
 */
function unsafeClearLocalStorage() {
    console.warn('⚠️ UNSAFE: Clearing ALL localStorage data including activation');
    localStorage.clear();
}

/**
 * Очищает localStorage, сохраняя только счетчик запусков (БЕЗ активации)
 * Используется при очистке базы данных - пользователь должен заново активировать
 */
function clearWithTrialDataOnly() {
    console.log('🔄 Clearing localStorage but preserving trial data only...');

    // Сохраняем только данные о запусках
    const trialData = {
        trialCount: localStorage.getItem('passwordManager_trialCount'),
        firstRunDate: localStorage.getItem('passwordManager_firstRunDate')
    };

    console.log('💾 Saved trial data:', {
        trialCount: trialData.trialCount,
        firstRunDate: !!trialData.firstRunDate
    });

    // Очищаем localStorage полностью
    localStorage.clear();
    console.log('🗑️ localStorage cleared');

    // Восстанавливаем только данные о запусках (НЕ активацию)
    if (trialData.trialCount) {
        localStorage.setItem('passwordManager_trialCount', trialData.trialCount);
        console.log('✅ Restored trial count');
    }
    if (trialData.firstRunDate) {
        localStorage.setItem('passwordManager_firstRunDate', trialData.firstRunDate);
        console.log('✅ Restored first run date');
    }

    console.log('🔄 Database cleared: activation RESET, trial data preserved');
    console.log('🔑 User will need to activate again');
}

/**
 * Показывает текущие данные активации в localStorage
 */
function showActivationData() {
    console.log('📊 Current activation data in localStorage:');
    console.log('- Activated:', localStorage.getItem('passwordManager_activated'));
    console.log('- Trial count:', localStorage.getItem('passwordManager_trialCount'));
    console.log('- First run date:', localStorage.getItem('passwordManager_firstRunDate'));
}

// Делаем функции глобально доступными
window.safeClearLocalStorage = safeClearLocalStorage;
window.unsafeClearLocalStorage = unsafeClearLocalStorage;
window.clearWithTrialDataOnly = clearWithTrialDataOnly;
window.clearAllIncludingActivation = clearAllIncludingActivation;
window.showActivationData = showActivationData;
