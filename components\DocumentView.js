function DocumentView({ document, onDelete, onEdit }) {
    if (!document) {
        console.error('Document<PERSON>iew received undefined document');
        return null;
    }
    
    try {
        const [showData, setShowData] = React.useState(false);
        
        React.useEffect(() => {
            if (document && document.id && typeof localStorage !== 'undefined') {
                const saved = localStorage.getItem(`document_${document.id}_showData`);
                setShowData(saved === 'true');
            }
        }, [document]);
        
        React.useEffect(() => {
            if (document && document.id && typeof localStorage !== 'undefined') {
                localStorage.setItem(`document_${document.id}_showData`, showData ? 'true' : 'false');
            }
        }, [showData, document]);

        const handleCopy = async (text, field) => {
            try {
                const success = await copyToClipboard(text);
                if (success) {
                    showToast(`${field} скопирован в буфер обмена`);
                }
            } catch (error) {
                console.error('Copy error:', error);
                showToast('Не удалось скопировать в буфер обмена', 'error');
            }
        };

        const handleDelete = async () => {
            if (confirm('Вы уверены, что хотите удалить этот документ?')) {
                try {
                    await onDelete(document.id);
                } catch (error) {
                    console.error('Delete document error:', error);
                    showToast('Не удалось удалить документ', 'error');
                }
            }
        };

        const maskData = (data) => {
            if (!data) return '•••••';
            return showData ? data : '•'.repeat(data.length);
        };

        return (
            <div className="glass-effect rounded-xl p-6 card-hover" data-name="document-view">
                <div className="flex justify-between items-start mb-4">
                    <h3 className="text-lg font-semibold text-white">{document.title}</h3>
                    <div className="flex space-x-2">
                        <button
                            onClick={() => setShowData(!showData)}
                            className="text-gray-400 hover:text-gray-300"
                            type="button"
                        >
                            <i className={`fas ${showData ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                        </button>
                        <button
                            onClick={() => onEdit(document)}
                            className="text-blue-400 hover:text-blue-300"
                            type="button"
                        >
                            <i className="fas fa-edit"></i>
                        </button>
                        <button
                            onClick={handleDelete}
                            className="text-red-400 hover:text-red-300 transition-colors"
                            type="button"
                        >
                            <i className="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <div className="space-y-3">
                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <label className="text-sm text-gray-400">Серия и номер паспорта</label>
                            <p className="font-mono" style={{color: document.fieldColors?.passportSeries || '#ffffff'}}>
                                {maskData(document.passportSeries)}
                            </p>
                            <p className="font-mono" style={{color: document.fieldColors?.passportNumber || '#ffffff'}}>
                                {maskData(document.passportNumber)}
                            </p>
                        </div>
                        <button
                            onClick={() => handleCopy(`${document.passportSeries} ${document.passportNumber}`, 'Серия и номер паспорта')}
                            className="text-blue-400 hover:text-blue-300"
                            type="button"
                        >
                            <i className="fas fa-copy"></i>
                        </button>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <label className="text-sm text-gray-400">Дата выдачи</label>
                            <p style={{color: document.fieldColors?.passportIssueDate || '#ffffff'}}>{maskData(document.passportIssueDate)}</p>
                        </div>
                        <button
                            onClick={() => handleCopy(document.passportIssueDate, 'Дата выдачи')}
                            className="text-blue-400 hover:text-blue-300"
                            type="button"
                        >
                            <i className="fas fa-copy"></i>
                        </button>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <label className="text-sm text-gray-400">Кем выдан</label>
                            <p style={{
                                color: document.fieldColors?.passportIssuedBy || '#ffffff',
                                fontFamily: document.fontFamily || 'Inter'
                            }}>{maskData(document.passportIssuedBy)}</p>
                        </div>
                        <button
                            onClick={() => handleCopy(document.passportIssuedBy, 'Кем выдан')}
                            className="text-blue-400 hover:text-blue-300"
                            type="button"
                        >
                            <i className="fas fa-copy"></i>
                        </button>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <label className="text-sm text-gray-400">Код подразделения</label>
                            <p style={{color: document.fieldColors?.passportDepartmentCode || '#ffffff'}}>{maskData(document.passportDepartmentCode)}</p>
                        </div>
                        <button
                            onClick={() => handleCopy(document.passportDepartmentCode, 'Код подразделения')}
                            className="text-blue-400 hover:text-blue-300"
                            type="button"
                        >
                            <i className="fas fa-copy"></i>
                        </button>
                    </div>

                    {document.inn && (
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <label className="text-sm text-gray-400">ИНН</label>
                                <p style={{color: document.fieldColors?.inn || '#ffffff'}}>{maskData(document.inn)}</p>
                            </div>
                            <button
                                onClick={() => handleCopy(document.inn, 'ИНН')}
                                className="text-blue-400 hover:text-blue-300"
                                type="button"
                            >
                                <i className="fas fa-copy"></i>
                            </button>
                        </div>
                    )}

                    {document.snils && (
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <label className="text-sm text-gray-400">СНИЛС</label>
                                <p style={{color: document.fieldColors?.snils || '#ffffff'}}>{maskData(document.snils)}</p>
                            </div>
                            <button
                                onClick={() => handleCopy(document.snils, 'СНИЛС')}
                                className="text-blue-400 hover:text-blue-300"
                                type="button"
                            >
                                <i className="fas fa-copy"></i>
                            </button>
                        </div>
                    )}

                    {document.notes && (
                        <div>
                            <label className="text-sm text-gray-400">Дополнительная информация</label>
                            <p className="text-sm" style={{color: document.fieldColors?.notes || '#ffffff'}}>{document.notes}</p>
                        </div>
                    )}
                </div>
            </div>
        );
    } catch (error) {
        console.error('DocumentView component error:', error);
        reportError(error);
    }
}