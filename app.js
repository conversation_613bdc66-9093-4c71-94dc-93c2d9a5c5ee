function App() {
    try {
        const [isLoggedIn, setIsLoggedIn] = React.useState(false);
        const [isFirstTime, setIsFirstTime] = React.useState(false);
        const [loading, setLoading] = React.useState(true);
        const [showResetPassword, setShowResetPassword] = React.useState(false);
        const [showActivationDialog, setShowActivationDialog] = React.useState(false);
        const [showTrialNotification, setShowTrialNotification] = React.useState(false);
        const [isActivated, setIsActivated] = React.useState(false);
        const [trialExpired, setTrialExpired] = React.useState(false);
        
        // Компонент для отображения уведомления после очистки базы данных
        const dbClearedNotification = <DbClearedNotification />;

        React.useEffect(() => {
            initializeApp();

            // Проверяем, был ли пользователь авторизован ранее
            const wasLoggedIn = localStorage.getItem('passwordManagerLoggedIn');
            if (!wasLoggedIn) {
                // Если нет сохраненного состояния входа, сбрасываем состояние
                setIsLoggedIn(false);
            }
        }, []);

        const initializeApp = async () => {
            try {
                // Сначала инициализируем базу данных
                console.log('Initializing database...');

                // Инициализируем базу данных и делаем её глобально доступной
                try {
                    window.db = await initDatabase();
                } catch (dbError) {
                    console.error('Database initialization error:', dbError);
                    // Пробуем открыть базу данных напрямую
                    const request = indexedDB.open('passwordManagerDB', 10);
                    window.db = await new Promise((resolve, reject) => {
                        request.onsuccess = (event) => resolve(event.target.result);
                        request.onerror = (event) => reject(event.target.error);
                    });
                }

                if (!window.db) {
                    throw new Error('Failed to initialize database');
                }

                console.log('Database initialized successfully');

                // Теперь проверяем активацию программы
                const activated = isActivated();
                setIsActivated(activated);

                if (!activated) {
                    // Увеличиваем счетчик запусков демоверсии
                    const trialCount = incrementTrialCount();
                    console.log('Trial run:', trialCount);

                    // Проверяем, не истек ли пробный период
                    if (isTrialExpired()) {
                        setTrialExpired(true);
                        setShowActivationDialog(true);
                        setLoading(false);
                        return;
                    } else {
                        // Показываем уведомление о демоверсии
                        setShowTrialNotification(true);
                    }
                }

                // Проверяем, является ли это первым запуском приложения
                const firstRun = isFirstRun();
                console.log('Is first run:', firstRun);

                // Проверяем наличие мастер-пароля
                const masterPassword = await getMasterPassword();
                console.log('App initialization - Master password exists:', !!masterPassword);
                
                // Проверяем localStorage для резервной копии пароля
                const localStoragePassword = getPasswordFromLocalStorage();
                
                // Если пароль есть только в localStorage, восстанавливаем его в IndexedDB
                if (!masterPassword && localStoragePassword) {
                    console.log('Restoring master password from localStorage to IndexedDB');
                    await saveMasterPassword(localStoragePassword);
                    // Повторно проверяем наличие мастер-пароля
                    const restoredPassword = await getMasterPassword();
                    setIsFirstTime(!restoredPassword);
                } else {
                    // Если это первый запуск, показываем экран создания пароля
                    // В противном случае, проверяем наличие пароля
                    setIsFirstTime(firstRun || !masterPassword);
                }
            } catch (error) {
                console.error('App initialization error:', error);
                showToast('Ошибка инициализации приложения', 'error');
            } finally {
                setLoading(false);
            }
        };
        
        // Вспомогательная функция для проверки наличия пароля в IndexedDB
        const checkPasswordInIndexedDB = async () => {
            try {
                const database = window.db;
                if (!database) return false;
                
                const transaction = database.transaction(['settings'], 'readonly');
                const store = transaction.objectStore('settings');
                
                return new Promise((resolve) => {
                    const request = store.get('masterPassword');
                    request.onsuccess = () => resolve(!!request.result);
                    request.onerror = () => resolve(false);
                });
            } catch (error) {
                console.error('Error checking password in IndexedDB:', error);
                return false;
            }
        };

        const handleLogin = () => {
            setIsLoggedIn(true);
            // Сохраняем состояние входа в localStorage
            localStorage.setItem('passwordManagerLoggedIn', 'true');
        };

        const handleLogout = () => {
            // Удаляем состояние авторизации из localStorage
            localStorage.removeItem('passwordManagerLoggedIn');
            // Перезагружаем приложение
            window.location.reload();
        };
        
        const handleResetPassword = () => {
            setShowResetPassword(true);
        };
        
        const handleCancelReset = () => {
            setShowResetPassword(false);
            // Если пользователь отменил сброс пароля, проверяем, был ли он авторизован
            const wasLoggedIn = localStorage.getItem('passwordManagerLoggedIn');
            if (!wasLoggedIn) {
                setIsLoggedIn(false);
            }
        };

        const handleActivation = () => {
            setIsActivated(true);
            setShowActivationDialog(false);
            setShowTrialNotification(false);
            setTrialExpired(false);
            showToast('Добро пожаловать в полную версию Password Manager!', 'success');
        };

        const handleShowActivationDialog = () => {
            setShowTrialNotification(false);
            setShowActivationDialog(true);
        };

        const handleContinueTrial = () => {
            setShowTrialNotification(false);
        };

        const handleCancelActivation = () => {
            if (!trialExpired) {
                setShowActivationDialog(false);
            }
        };

        if (loading) {
            return (
                <div className="min-h-screen bg-gray-900 flex items-center justify-center" data-name="loading" data-file="app.js">
                    <div className="text-center">
                        <i className="fas fa-spinner fa-spin text-4xl text-blue-400 mb-4"></i>
                        <p className="text-gray-400">Initializing Password Manager...</p>
                    </div>
                </div>
            );
        }
        
        // Проверяем активацию перед показом основного интерфейса
        if (showActivationDialog) {
            return <ActivationDialog
                onActivate={handleActivation}
                onCancel={handleCancelActivation}
                isTrialExpired={trialExpired}
            />;
        }

        if (showResetPassword) {
            return <ResetPasswordForm onCancel={handleCancelReset} />;
        }

        return (
            <div className="App" data-name="app" data-file="app.js">
                {dbClearedNotification}

                {/* Уведомление о демоверсии */}
                {showTrialNotification && !isActivated && (
                    <TrialNotification
                        onActivate={handleShowActivationDialog}
                        onContinue={handleContinueTrial}
                    />
                )}

                {isLoggedIn ? (
                    <Dashboard onLogout={handleLogout} onResetPassword={handleResetPassword} />
                ) : (
                    <LoginForm
                        onLogin={handleLogin}
                        isFirstTime={isFirstTime}
                        onResetPassword={handleResetPassword}
                    />
                )}
            </div>
        );
    } catch (error) {
        console.error('App component error:', error);
        // Определение функции reportError, если она не существует
        if (typeof reportError !== 'function') {
            window.reportError = function(err) {
                console.error('Error reported:', err);
            };
        }
        reportError(error);
    }
}

ReactDOM.render(<App />, document.getElementById('root'));
