// Database utilities for password management
const DB_NAME = 'passwordManagerDB';
const DB_VERSION = 10; // Увеличиваем версию для добавления таблиц

let db = null;

const initDatabase = () => {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open(DB_NAME, DB_VERSION);
        
        request.onerror = () => {
            console.error('Error opening database:', request.error);
            reject(request.error);
        };
        
        request.onupgradeneeded = (event) => {
            console.log('Upgrading database to version', DB_VERSION);
            const database = event.target.result;
            const oldVersion = event.oldVersion;
            
            if (!database.objectStoreNames.contains('passwords')) {
                const store = database.createObjectStore('passwords', { keyPath: 'id', autoIncrement: true });
                store.createIndex('email', 'email', { unique: false });
                store.createIndex('category', 'category', { unique: false });
            } else if (oldVersion < 4) {
                // Если обновляемся с версии ниже 4, добавляем поддержку для телефона
                console.log('Upgrading database to version 4 with phone support');
            }
            
            if (!database.objectStoreNames.contains('cards')) {
                const cardStore = database.createObjectStore('cards', { keyPath: 'id', autoIncrement: true });
                cardStore.createIndex('cardNumber', 'cardNumber', { unique: false });
            }
            
            if (!database.objectStoreNames.contains('settings')) {
                database.createObjectStore('settings', { keyPath: 'key' });
            }
            
            if (!database.objectStoreNames.contains('tabs')) {
                database.createObjectStore('tabs', { keyPath: 'id', autoIncrement: true });
            }
            
            if (!database.objectStoreNames.contains('notes')) {
                const notesStore = database.createObjectStore('notes', { keyPath: 'id', autoIncrement: true });
                notesStore.createIndex('title', 'title', { unique: false });
                notesStore.createIndex('createdAt', 'createdAt', { unique: false });
            }
            
            if (!database.objectStoreNames.contains('documents')) {
                const documentsStore = database.createObjectStore('documents', { keyPath: 'id', autoIncrement: true });
                documentsStore.createIndex('type', 'type', { unique: false });
                documentsStore.createIndex('createdAt', 'createdAt', { unique: false });
            }

            if (!database.objectStoreNames.contains('tables')) {
                const tablesStore = database.createObjectStore('tables', { keyPath: 'id', autoIncrement: true });
                tablesStore.createIndex('name', 'name', { unique: false });
                tablesStore.createIndex('createdAt', 'createdAt', { unique: false });
            }
        };
        
        request.onsuccess = async () => {
            console.log('Database opened successfully');
            db = request.result;
            window.db = db; // Делаем базу данных глобально доступной
            
            try {
                // Проверяем наличие мастер-пароля в localStorage и восстанавливаем его в IndexedDB при необходимости
                const localStoragePassword = localStorage.getItem('masterPasswordHash');
                if (localStoragePassword) {
                    const transaction = db.transaction(['settings'], 'readonly');
                    const store = transaction.objectStore('settings');
                    const passwordRequest = store.get('masterPassword');
                    
                    passwordRequest.onsuccess = () => {
                        if (!passwordRequest.result || !passwordRequest.result.value) {
                            console.log('Restoring master password from localStorage to IndexedDB');
                            saveMasterPassword(localStoragePassword);
                        }
                    };
                }
                
                // Инициализируем вкладки
                const existingTabs = await getTabs();
                if (existingTabs.length === 0) {
                    await initializeDefaultTabs();
                }
            } catch (error) {
                console.error('Error during database initialization:', error);
            }
            
            resolve(db);
        };
    });
};

const initializeDefaultTabs = async () => {
    const defaultTabs = [
        { name: 'Банковские карты', isDefault: true, type: 'cards', locked: true },
        { name: 'Все', isDefault: true },
        { name: 'Соцсети', isDefault: true },
        { name: 'Почта', isDefault: true },
        { name: 'Игры', isDefault: true },
        { name: 'Развлечения', isDefault: true },
        { name: 'Работа', isDefault: true }
    ];
    
    const transaction = db.transaction(['tabs'], 'readwrite');
    const store = transaction.objectStore('tabs');
    
    for (const tab of defaultTabs) {
        await new Promise((resolve, reject) => {
            const request = store.add(tab);
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }
};

const savePassword = async (passwordData) => {
    try {
        console.log('Saving password data:', passwordData);
        // Используем глобальную переменную db, если локальная не определена
        const database = window.db || db;
        if (!database) {
            throw new Error('Database is not initialized');
        }
        
        const transaction = database.transaction(['passwords'], 'readwrite');
        const store = transaction.objectStore('passwords');
        return new Promise((resolve, reject) => {
            const request = passwordData.id 
                ? store.put({
                    ...passwordData,
                    updatedAt: new Date().toISOString()
                  })
                : store.add({
                    ...passwordData,
                    createdAt: new Date().toISOString()
                  });
                
            request.onsuccess = () => {
                console.log('Password saved successfully with ID:', request.result);
                resolve(request.result);
            };
            request.onerror = (event) => {
                console.error('Error saving password:', event.target.error);
                reject(request.error);
            };
        });
    } catch (error) {
        console.error('Exception in savePassword:', error);
        throw error;
    }
};

const saveCard = async (cardData) => {
    try {
        // Используем глобальную переменную db, если локальная не определена
        const database = window.db || db;
        if (!database) {
            throw new Error('Database is not initialized');
        }
        
        // Создаем копию данных, чтобы не изменять оригинальный объект
        const cardToSave = { ...cardData };
        
        // Форматируем номер карты, если он есть
        if (cardToSave.cardNumber) {
            const cleaned = cardToSave.cardNumber.replace(/\s+/g, '');
            cardToSave.cardNumber = cleaned.replace(/(.{4})/g, '$1 ').trim();
        }
        
        const transaction = database.transaction(['cards'], 'readwrite');
        const store = transaction.objectStore('cards');
        return new Promise((resolve, reject) => {
            const request = store.add({
                ...cardToSave,
                createdAt: new Date().toISOString()
            });
            request.onsuccess = () => {
                console.log('Card saved successfully with ID:', request.result);
                resolve(request.result);
            };
            request.onerror = (event) => {
                console.error('Error saving card:', event.target.error);
                reject(request.error);
            };
        });
    } catch (error) {
        console.error('Exception in saveCard:', error);
        throw error;
    }
};

const getPasswords = async (category = null) => {
    const transaction = db.transaction(['passwords'], 'readonly');
    const store = transaction.objectStore('passwords');
    
    return new Promise((resolve, reject) => {
        if (!category || category === 'Все') {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        } else {
            const request = store.getAll();
            request.onsuccess = () => {
                const allPasswords = request.result;
                const filteredPasswords = allPasswords.filter(password => 
                    password.category === category
                );
                resolve(filteredPasswords);
            };
            request.onerror = () => reject(request.error);
        }
    });
};

const getCards = async () => {
    try {
        // Используем глобальную переменную db, если локальная не определена
        const database = window.db || db;
        if (!database) {
            throw new Error('Database is not initialized');
        }
        
        const transaction = database.transaction(['cards'], 'readonly');
        const store = transaction.objectStore('cards');
        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    } catch (error) {
        console.error('Exception in getCards:', error);
        throw error;
    }
};

const deletePassword = async (id) => {
    const transaction = db.transaction(['passwords'], 'readwrite');
    const store = transaction.objectStore('passwords');
    return new Promise((resolve, reject) => {
        const request = store.delete(id);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
    });
};

const deleteCard = async (id) => {
    try {
        // Используем глобальную переменную db, если локальная не определена
        const database = window.db || db;
        if (!database) {
            throw new Error('Database is not initialized');
        }
        
        const transaction = database.transaction(['cards'], 'readwrite');
        const store = transaction.objectStore('cards');
        return new Promise((resolve, reject) => {
            const request = store.delete(id);
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    } catch (error) {
        console.error('Exception in deleteCard:', error);
        throw error;
    }
};

const saveMasterPassword = async (hashedPassword) => {
    try {
        if (!hashedPassword) {
            throw new Error('Hashed password is required');
        }
        
        const database = window.db || db;
        if (!database) {
            throw new Error('Database is not initialized');
        }
        
        // Сохраняем в IndexedDB
        const transaction = database.transaction(['settings'], 'readwrite');
        const store = transaction.objectStore('settings');
        
        const result = await new Promise((resolve, reject) => {
            const request = store.put({ key: 'masterPassword', value: hashedPassword });
            request.onsuccess = () => {
                console.log('Master password saved successfully to IndexedDB');
                resolve(request.result);
            };
            request.onerror = (event) => {
                console.error('Error saving master password to IndexedDB:', event.target.error);
                reject(request.error);
            };
        });
        
        // Дублируем в localStorage для надежности
        savePasswordToLocalStorage(hashedPassword);
        
        return result;
    } catch (error) {
        console.error('Exception in saveMasterPassword:', error);
        throw error;
    }
};

const getMasterPassword = async () => {
    try {
        console.log('getMasterPassword: Starting password retrieval...');

        // Сначала пробуем получить из IndexedDB
        let password = null;

        try {
            const database = window.db || db;
            console.log('getMasterPassword: Database available:', !!database);

            if (database) {
                const transaction = database.transaction(['settings'], 'readonly');
                const store = transaction.objectStore('settings');

                password = await new Promise((resolve, reject) => {
                    const request = store.get('masterPassword');
                    request.onsuccess = () => {
                        const result = request.result ? request.result.value : null;
                        console.log('getMasterPassword: Retrieved from IndexedDB:', !!result);
                        if (result) {
                            console.log('getMasterPassword: IndexedDB password hash (first 10 chars):', result.substring(0, 10) + '...');
                        }
                        resolve(result);
                    };
                    request.onerror = () => {
                        console.error('getMasterPassword: Error getting from IndexedDB:', request.error);
                        reject(request.error);
                    };
                });
            }
        } catch (dbError) {
            console.error('getMasterPassword: Error accessing IndexedDB:', dbError);
        }

        // Если не удалось получить из IndexedDB, пробуем из localStorage
        if (!password) {
            password = getPasswordFromLocalStorage();
            console.log('getMasterPassword: Retrieved from localStorage:', !!password);
            if (password) {
                console.log('getMasterPassword: localStorage password hash (first 10 chars):', password.substring(0, 10) + '...');
            }

            // Если нашли в localStorage, но нет в IndexedDB, сохраняем в IndexedDB
            if (password && window.db) {
                try {
                    console.log('getMasterPassword: Syncing password from localStorage to IndexedDB...');
                    await saveMasterPassword(password);
                } catch (saveError) {
                    console.error('getMasterPassword: Error saving password from localStorage to IndexedDB:', saveError);
                }
            }
        }

        console.log('getMasterPassword: Final result:', !!password);
        return password;
    } catch (error) {
        console.error('getMasterPassword: Exception:', error);
        return null;
    }
};

const getTabs = async () => {
    const transaction = db.transaction(['tabs'], 'readonly');
    const store = transaction.objectStore('tabs');
    return new Promise((resolve, reject) => {
        const request = store.getAll();
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
    });
};

const saveTab = async (tabData) => {
    const transaction = db.transaction(['tabs'], 'readwrite');
    const store = transaction.objectStore('tabs');
    return new Promise((resolve, reject) => {
        const request = store.add({ ...tabData, isDefault: false });
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
    });
};

const updateTab = async (id, tabData) => {
    const transaction = db.transaction(['tabs'], 'readwrite');
    const store = transaction.objectStore('tabs');
    return new Promise((resolve, reject) => {
        const request = store.put({ id, ...tabData });
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
    });
};

const deleteTab = async (id) => {
    const transaction = db.transaction(['tabs'], 'readwrite');
    const store = transaction.objectStore('tabs');
    return new Promise((resolve, reject) => {
        const request = store.delete(id);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
    });
};
