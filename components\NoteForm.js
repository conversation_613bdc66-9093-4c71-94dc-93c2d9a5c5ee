function NoteForm({ onAdd, onCancel, editNote = null }) {
    try {
        const [formData, setFormData] = React.useState(
            editNote ? {
                ...editNote
            } : {
                title: '',
                content: '',
                color: '#FFFFFF',
                font: 'sans-serif',
                titleColor: '#FFFFFF',
                titleFont: 'sans-serif',
                contentColor: '#FFFFFF',
                contentFont: 'sans-serif',
                images: []
            }
        );
        
        const [selectedImage, setSelectedImage] = React.useState(null);
        
        const colorOptions = [
            { name: 'White', value: '#FFFFFF' },
            { name: 'Blue', value: '#3B82F6' },
            { name: 'Green', value: '#10B981' },
            { name: 'Purple', value: '#8B5CF6' },
            { name: 'Red', value: '#EF4444' },
            { name: 'Yellow', value: '#F59E0B' },
            { name: 'Pink', value: '#EC4899' },
            { name: '<PERSON>e', value: '#84CC16' },
            { name: 'Orange', value: '#F97316' },
            { name: 'Tea<PERSON>', value: '#14B8A6' },
            { name: '<PERSON><PERSON>', value: '#06B6D4' },
            { name: 'Indigo', value: '#6366F1' },
            { name: 'Gray', value: '#9CA3AF' }
        ];
        
        const fontOptions = [
            { name: 'Sans-serif', value: 'sans-serif' },
            { name: 'Serif', value: 'serif' },
            { name: 'Monospace', value: 'monospace' },
            { name: 'Cursive', value: 'cursive' },
            { name: 'Fantasy', value: 'fantasy' },
            { name: 'Arial', value: 'Arial, sans-serif' },
            { name: 'Verdana', value: 'Verdana, sans-serif' },
            { name: 'Tahoma', value: 'Tahoma, sans-serif' },
            { name: 'Georgia', value: 'Georgia, serif' },
            { name: 'Courier New', value: 'Courier New, monospace' },
            { name: 'Brush Script', value: 'Brush Script MT, cursive' },
            { name: 'Impact', value: 'Impact, fantasy' }
        ];
        
        const handleSubmit = async (e) => {
            e.preventDefault();
            
            if (!formData.title) {
                showToast('Пожалуйста, введите заголовок заметки', 'error');
                return;
            }
            
            try {
                // Сохраняем цвет и шрифт для отображения
                const noteToSave = {
                    ...formData,
                    color: formData.contentColor || formData.color,
                    font: formData.contentFont || formData.font
                };
                
                await saveNote(noteToSave);
                showToast('Заметка сохранена успешно');
                onAdd();
            } catch (error) {
                console.error('Ошибка сохранения заметки:', error);
                showToast('Не удалось сохранить заметку', 'error');
            }
        };
        
        const handleImageUpload = (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            if (!file.type.startsWith('image/')) {
                showToast('Пожалуйста, выберите изображение', 'error');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = (event) => {
                const imageData = event.target.result;
                setSelectedImage(imageData);
                setFormData({
                    ...formData,
                    images: [...formData.images, imageData]
                });
            };
            reader.readAsDataURL(file);
        };
        
        const removeImage = (index) => {
            const updatedImages = [...formData.images];
            updatedImages.splice(index, 1);
            setFormData({
                ...formData,
                images: updatedImages
            });
        };
        
        return (
            <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
                <div className="glass-effect rounded-2xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-xl font-bold">{editNote ? 'Редактировать заметку' : 'Новая заметка'}</h2>
                        <button onClick={onCancel} className="text-gray-400 hover:text-white">
                            <i className="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Заголовок заметки */}
                        <div className="space-y-2">
                            <div className="flex justify-between items-center">
                                <label className="text-sm text-gray-300 font-medium">Заголовок</label>
                                <div className="flex items-center space-x-3">
                                    <div className="flex space-x-1">
                                        {colorOptions.map(color => (
                                            <button
                                                key={color.value}
                                                type="button"
                                                onClick={() => setFormData({ ...formData, titleColor: color.value })}
                                                className={`w-5 h-5 rounded-full border transition-all ${
                                                    formData.titleColor === color.value ? 'border-white scale-110' : 'border-gray-600'
                                                }`}
                                                style={{ backgroundColor: color.value }}
                                            ></button>
                                        ))}
                                    </div>
                                    <select 
                                        value={formData.titleFont || 'sans-serif'} 
                                        onChange={(e) => setFormData({ ...formData, titleFont: e.target.value })}
                                        className="text-sm bg-gray-800 border border-gray-700 rounded px-2 py-1 text-gray-300"
                                    >
                                        {fontOptions.map(font => (
                                            <option key={font.value} value={font.value}>{font.name}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            <input
                                type="text"
                                placeholder="Заголовок заметки"
                                value={formData.title}
                                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none text-lg"
                                style={{ color: formData.titleColor || '#FFFFFF', fontFamily: formData.titleFont || 'sans-serif' }}
                            />
                        </div>
                        
                        {/* Содержание заметки */}
                        <div className="space-y-2">
                            <div className="flex justify-between items-center">
                                <label className="text-sm text-gray-300 font-medium">Содержание</label>
                                <div className="flex items-center space-x-3">
                                    <div className="flex space-x-1">
                                        {colorOptions.map(color => (
                                            <button
                                                key={color.value}
                                                type="button"
                                                onClick={() => setFormData({ ...formData, contentColor: color.value })}
                                                className={`w-5 h-5 rounded-full border transition-all ${
                                                    formData.contentColor === color.value ? 'border-white scale-110' : 'border-gray-600'
                                                }`}
                                                style={{ backgroundColor: color.value }}
                                            ></button>
                                        ))}
                                    </div>
                                    <select 
                                        value={formData.contentFont || 'sans-serif'} 
                                        onChange={(e) => setFormData({ ...formData, contentFont: e.target.value })}
                                        className="text-sm bg-gray-800 border border-gray-700 rounded px-2 py-1 text-gray-300"
                                    >
                                        {fontOptions.map(font => (
                                            <option key={font.value} value={font.value}>{font.name}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            <textarea
                                placeholder="Текст заметки"
                                value={formData.content}
                                onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none resize-none"
                                style={{ color: formData.contentColor || '#FFFFFF', fontFamily: formData.contentFont || 'sans-serif' }}
                                rows="12"
                            ></textarea>
                        </div>
                        
                        {/* Изображения */}
                        <div className="space-y-3">
                            <label className="text-sm text-gray-300 font-medium">Изображения</label>
                            <div className="flex flex-wrap gap-3">
                                {formData.images && formData.images.map((image, index) => (
                                    <div key={index} className="relative w-32 h-32 group">
                                        <img 
                                            src={image} 
                                            alt={`Изображение ${index + 1}`} 
                                            className="w-full h-full object-cover rounded-lg"
                                        />
                                        <button
                                            type="button"
                                            onClick={() => removeImage(index)}
                                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                                        >
                                            <i className="fas fa-times text-xs"></i>
                                        </button>
                                    </div>
                                ))}
                                <label className="w-32 h-32 border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center cursor-pointer hover:border-gray-400 transition-colors">
                                    <i className="fas fa-plus text-gray-400 text-xl"></i>
                                    <input 
                                        type="file" 
                                        accept="image/*" 
                                        onChange={handleImageUpload} 
                                        className="hidden" 
                                    />
                                </label>
                            </div>
                        </div>
                        
                        {/* Кнопки */}
                        <div className="flex space-x-4 pt-4">
                            <button
                                type="submit"
                                className="flex-1 py-3 btn-primary rounded-lg font-semibold text-lg"
                            >
                                {editNote ? 'Обновить' : 'Сохранить'}
                            </button>
                            <button
                                type="button"
                                onClick={onCancel}
                                className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold transition-colors text-lg"
                            >
                                Отмена
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    } catch (error) {
        console.error('NoteForm component error:', error);
        reportError(error);
    }
}