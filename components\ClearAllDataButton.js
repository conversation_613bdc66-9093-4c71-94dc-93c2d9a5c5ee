function ClearAllDataButton() {
    const clearAllData = async () => {
        const confirmMessage = `⚠️ ВНИМАНИЕ: Полная очистка данных

Будет удалено:
• Все пароли и карты
• Все заметки и документы
• Настройки приложения
• КЛЮЧ ЛИЦЕНЗИИ (потребуется заново вводить ключ активации)

Сохранится:
• Счетчик запусков программы

Это действие НЕВОЗМОЖНО отменить!

Продолжить?`;

        if (confirm(confirmMessage)) {
            try {
                // Очищаем с сохранением только данных о запусках (БЕЗ активации)
                if (typeof clearWithTrialDataOnly === 'function') {
                    clearWithTrialDataOnly();
                } else {
                    // Fallback если функция не загружена
                    const trialCount = localStorage.getItem('passwordManager_trialCount');
                    const firstRunDate = localStorage.getItem('passwordManager_firstRunDate');
                    localStorage.clear();
                    if (trialCount) localStorage.setItem('passwordManager_trialCount', trialCount);
                    if (firstRunDate) localStorage.setItem('passwordManager_firstRunDate', firstRunDate);
                    console.log('🔄 All data cleared: activation reset, trial count preserved');
                }
                
                // Удаляем базу данных напрямую
                const request = indexedDB.deleteDatabase('passwordManagerDB');
                
                request.onsuccess = () => {
                    showToast('Все данные успешно удалены. Страница будет перезагружена.');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                };
                
                request.onerror = (event) => {
                    console.error('Ошибка при удалении базы данных:', event);
                    showToast('Произошла ошибка при удалении данных', 'error');
                };
            } catch (error) {
                console.error('Ошибка при очистке данных:', error);
                showToast('Произошла ошибка при удалении данных', 'error');
            }
        }
    };
    
    return (
        <button 
            onClick={clearAllData}
            className="mt-4 text-red-500 hover:text-red-400 text-sm"
        >
            Удалить все данные
        </button>
    );
}