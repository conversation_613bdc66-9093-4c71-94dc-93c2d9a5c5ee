function ClearAllDataButton() {
    const clearAllData = async () => {
        if (confirm('Вы уверены, что хотите удалить ВСЕ данные? Это действие нельзя отменить.')) {
            try {
                // Безопасно очищаем localStorage с сохранением данных активации
                safeClearLocalStorage();
                
                // Удаляем базу данных напрямую
                const request = indexedDB.deleteDatabase('passwordManagerDB');
                
                request.onsuccess = () => {
                    showToast('Все данные успешно удалены. Страница будет перезагружена.');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                };
                
                request.onerror = (event) => {
                    console.error('Ошибка при удалении базы данных:', event);
                    showToast('Произошла ошибка при удалении данных', 'error');
                };
            } catch (error) {
                console.error('Ошибка при очистке данных:', error);
                showToast('Произошла ошибка при удалении данных', 'error');
            }
        }
    };
    
    return (
        <button 
            onClick={clearAllData}
            className="mt-4 text-red-500 hover:text-red-400 text-sm"
        >
            Удалить все данные
        </button>
    );
}