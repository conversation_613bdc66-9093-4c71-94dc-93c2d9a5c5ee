function ClearAllDataButton() {
    const clearAllData = async () => {
        const confirmMessage = `⚠️ ВНИМАНИЕ: Полная очистка данных

Будет удалено:
• Все пароли и карты
• Все заметки и документы
• Настройки приложения
• КЛЮЧ ЛИЦЕНЗИИ (потребуется заново вводить ключ активации)

Это действие НЕВОЗМОЖНО отменить!

Продолжить?`;

        if (confirm(confirmMessage)) {
            try {
                // Полностью очищаем localStorage включая ВСЕ данные активации
                localStorage.clear();
                console.log('🔄 All data cleared: ALL activation data reset (including trial count)');
                
                // Удаляем базу данных напрямую
                const request = indexedDB.deleteDatabase('passwordManagerDB');
                
                request.onsuccess = () => {
                    showToast('Все данные успешно удалены. Страница будет перезагружена.');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                };
                
                request.onerror = (event) => {
                    console.error('Ошибка при удалении базы данных:', event);
                    showToast('Произошла ошибка при удалении данных', 'error');
                };
            } catch (error) {
                console.error('Ошибка при очистке данных:', error);
                showToast('Произошла ошибка при удалении данных', 'error');
            }
        }
    };
    
    return (
        <button 
            onClick={clearAllData}
            className="mt-4 text-red-500 hover:text-red-400 text-sm"
        >
            Удалить все данные
        </button>
    );
}