// Отладочные функции для проверки состояния хранилища
function checkStorageState() {
    console.log('--- Storage State Check ---');

    // Проверка localStorage
    const localStoragePassword = localStorage.getItem('masterPasswordHash');
    console.log('Master password in localStorage:', !!localStoragePassword);
    if (localStoragePassword) {
        console.log('Master password hash (first 10 chars):', localStoragePassword.substring(0, 10) + '...');
    }

    // Проверка состояния авторизации
    const isLoggedIn = localStorage.getItem('passwordManagerLoggedIn');
    console.log('User logged in state:', isLoggedIn);

    // Проверка активации
    const activated = localStorage.getItem('passwordManager_activated');
    const trialCount = localStorage.getItem('passwordManager_trialCount');
    console.log('Activation status:', activated);
    console.log('Trial count:', trialCount);

    // Проверка IndexedDB
    checkIndexedDB();
}

// Функция для полного сброса приложения
function resetApp() {
    console.log('Resetting application...');

    // Безопасно очищаем localStorage с сохранением данных о запусках
    if (typeof clearWithTrialDataOnly === 'function') {
        clearWithTrialDataOnly();
    } else {
        // Fallback
        const trialCount = localStorage.getItem('passwordManager_trialCount');
        const firstRunDate = localStorage.getItem('passwordManager_firstRunDate');
        localStorage.clear();
        if (trialCount) localStorage.setItem('passwordManager_trialCount', trialCount);
        if (firstRunDate) localStorage.setItem('passwordManager_firstRunDate', firstRunDate);
    }

    // Удаляем IndexedDB
    const deleteRequest = indexedDB.deleteDatabase('passwordManagerDB');
    deleteRequest.onsuccess = () => {
        console.log('Database deleted successfully');
        alert('Приложение сброшено. Страница будет перезагружена.');
        window.location.reload();
    };
    deleteRequest.onerror = (error) => {
        console.error('Error deleting database:', error);
        alert('Ошибка при сбросе базы данных');
    };
}

// Функция для полного сброса приложения ВКЛЮЧАЯ данные активации
function resetAppCompletely() {
    console.log('⚠️ COMPLETE RESET: Resetting application including activation data...');

    const confirmMessage = `⚠️ ПОЛНЫЙ СБРОС ПРИЛОЖЕНИЯ

Будет удалено:
• Все пароли, карты, заметки
• База данных приложения
• КЛЮЧ ЛИЦЕНЗИИ и счетчик запусков
• ВСЕ настройки

Приложение вернется к первоначальному состоянию!

Продолжить?`;

    if (confirm(confirmMessage)) {
        // Полностью очищаем localStorage
        localStorage.clear();

        // Удаляем IndexedDB
        const deleteRequest = indexedDB.deleteDatabase('passwordManagerDB');
        deleteRequest.onsuccess = () => {
            console.log('Database deleted successfully');
            alert('Приложение полностью сброшено. Страница будет перезагружена.');
            window.location.reload();
        };
        deleteRequest.onerror = (error) => {
            console.error('Error deleting database:', error);
            alert('Ошибка при сбросе базы данных');
        };
    }
}

// Функция для тестирования пароля
async function testPassword(password) {
    console.log('Testing password:', password);
    try {
        const hashedPassword = await hashPassword(password);
        const storedPassword = await getMasterPassword();

        console.log('Test results:', {
            enteredPassword: password,
            hashedPassword: hashedPassword,
            storedPassword: storedPassword,
            match: hashedPassword === storedPassword
        });

        return hashedPassword === storedPassword;
    } catch (error) {
        console.error('Error testing password:', error);
        return false;
    }
}

// Функция для полной диагностики системы аутентификации
async function fullAuthDiagnostic() {
    console.log('=== FULL AUTHENTICATION DIAGNOSTIC ===');

    // 1. Проверка базы данных
    console.log('1. Database check:');
    console.log('   window.db exists:', !!window.db);
    console.log('   isDatabaseReady:', typeof isDatabaseReady === 'function' ? isDatabaseReady() : 'function not available');

    // 2. Проверка localStorage
    console.log('2. localStorage check:');
    const localPassword = localStorage.getItem('masterPasswordHash');
    console.log('   masterPasswordHash exists:', !!localPassword);
    if (localPassword) {
        console.log('   hash length:', localPassword.length);
        console.log('   hash preview:', localPassword.substring(0, 10) + '...');
    }

    // 3. Проверка IndexedDB
    console.log('3. IndexedDB check:');
    try {
        const dbPassword = await getMasterPassword();
        console.log('   getMasterPassword result:', !!dbPassword);
        if (dbPassword) {
            console.log('   hash length:', dbPassword.length);
            console.log('   hash preview:', dbPassword.substring(0, 10) + '...');
        }
    } catch (error) {
        console.log('   getMasterPassword error:', error.message);
    }

    // 4. Проверка функций
    console.log('4. Functions check:');
    console.log('   hashPassword available:', typeof hashPassword === 'function');
    console.log('   getMasterPassword available:', typeof getMasterPassword === 'function');
    console.log('   saveMasterPassword available:', typeof saveMasterPassword === 'function');

    // 5. Проверка активации
    console.log('5. Activation check:');
    if (typeof getActivationStatus === 'function') {
        const activationStatus = getActivationStatus();
        console.log('   activation status:', activationStatus);
    } else {
        console.log('   activation functions not available');
    }

    console.log('=== END DIAGNOSTIC ===');
}

// Функция для создания тестового пароля
async function createTestPassword(password = 'test123') {
    console.log('Creating test password:', password);
    try {
        // Простое ожидание базы данных
        let retries = 0;
        while (!window.db && retries < 50) {
            await new Promise(resolve => setTimeout(resolve, 100));
            retries++;
        }

        if (!window.db) {
            throw new Error('Database not ready');
        }

        const hashedPassword = await hashPassword(password);
        await saveMasterPassword(hashedPassword);
        console.log('Test password created successfully');
        return true;
    } catch (error) {
        console.error('Error creating test password:', error);
        return false;
    }
}

// Функция для проверки загрузки всех компонентов
function checkComponentsLoaded() {
    console.log('=== COMPONENTS CHECK ===');

    const components = {
        // Database functions
        'initDatabase': typeof initDatabase === 'function',
        'getMasterPassword': typeof getMasterPassword === 'function',
        'saveMasterPassword': typeof saveMasterPassword === 'function',

        // Crypto functions
        'hashPassword': typeof hashPassword === 'function',

        // Storage functions
        'isFirstRun': typeof isFirstRun === 'function',
        'getPasswordFromLocalStorage': typeof getPasswordFromLocalStorage === 'function',

        // Activation functions
        'isActivated': typeof isActivated === 'function',
        'getTrialCount': typeof getTrialCount === 'function',
        'incrementTrialCount': typeof incrementTrialCount === 'function',
        'isTrialExpired': typeof isTrialExpired === 'function',

        // Notification functions
        'showToast': typeof showToast === 'function'
    };

    let allLoaded = true;
    for (const [name, loaded] of Object.entries(components)) {
        console.log(`${loaded ? '✅' : '❌'} ${name}: ${loaded ? 'loaded' : 'NOT LOADED'}`);
        if (!loaded) allLoaded = false;
    }

    console.log('Database ready:', !!window.db);
    console.log('All components loaded:', allLoaded);
    console.log('========================');

    return allLoaded;
}

// Функция для принудительного тестирования активации
function forceShowActivation() {
    console.log('Forcing activation system test...');

    // Сбрасываем активацию
    localStorage.removeItem('passwordManager_activated');
    localStorage.removeItem('passwordManager_trialCount');
    localStorage.removeItem('passwordManager_firstRunDate');

    // Устанавливаем счетчик на 1
    localStorage.setItem('passwordManager_trialCount', '1');

    console.log('Activation reset. Reloading page...');
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// Функция для немедленного тестирования без перезагрузки
function testActivationNow() {
    console.log('=== TESTING ACTIVATION NOW ===');

    // Сбрасываем активацию
    localStorage.removeItem('passwordManager_activated');
    localStorage.setItem('passwordManager_trialCount', '2');

    // Проверяем функции
    if (typeof isActivated === 'function') {
        console.log('isActivated:', isActivated());
        console.log('getTrialCount:', getTrialCount());
        console.log('isTrialExpired:', isTrialExpired());

        // Если мы в Dashboard, принудительно показываем уведомление
        const event = new CustomEvent('forceActivationCheck');
        window.dispatchEvent(event);
    } else {
        console.error('Activation functions not loaded!');
    }
}

// Функция для тестирования истечения пробного периода
function forceTrialExpired() {
    console.log('Forcing trial expiration...');

    // Устанавливаем счетчик на максимум
    localStorage.setItem('passwordManager_trialCount', '10');
    localStorage.removeItem('passwordManager_activated');

    console.log('Trial expired. Reloading page...');
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// Простая функция для тестирования новой системы активации
function testNewActivation() {
    console.log('🧪 Testing new activation system...');

    // Сбрасываем активацию
    localStorage.removeItem('passwordManager_activated');
    localStorage.removeItem('passwordManager_trialCount');
    localStorage.removeItem('passwordManager_firstRunDate');

    console.log('✅ Activation data cleared.');
    console.log('📝 Instructions:');
    console.log('1. Reload the page');
    console.log('2. Login to the system');
    console.log('3. You should see the trial notification AFTER login');

    if (confirm('Reload page now to test?')) {
        window.location.reload();
    }
}

// Функция для тестирования с конкретным количеством запусков
function setTrialCount(count) {
    localStorage.removeItem('passwordManager_activated');
    localStorage.setItem('passwordManager_trialCount', (count - 1).toString());
    console.log(`🔢 Trial count set to ${count - 1} (next login will be run ${count})`);
    console.log('📝 Login to see the activation system in action');
}

// Функция для быстрого тестирования уведомления активации
function testActivationUI() {
    console.log('🎨 Testing activation UI...');
    setTrialCount(3);
    console.log('✅ Set to trial run 3');
    console.log('📝 Now login to see the cleaned up activation notification');
}

// Функция для тестирования диалога активации
function testActivationDialog() {
    console.log('🔑 Testing activation dialog...');
    setTrialCount(20); // Принудительная активация
    console.log('✅ Set to trial run 20 (forced activation)');
    console.log('📝 Now login to see the activation dialog');
}

// Функция для тестирования правильного ключа
function testCorrectKey() {
    console.log('🔑 Testing with correct activation key...');
    console.log('Key: neirotopchik_vk_2000_500-7000!!!!!');
    console.log('📝 Copy this key and test activation');
}

// Функция для тестирования кнопки активации
function testActivationButton() {
    console.log('🎯 Testing activation button...');

    // Убираем активацию чтобы показать кнопку
    localStorage.removeItem('passwordManager_activated');
    localStorage.setItem('passwordManager_trialCount', '5');

    console.log('✅ Activation removed - button should be visible');
    console.log('📝 Look for orange "Активация" button in Dashboard header');
    console.log('🔄 Refresh page to see the button');
    console.log('📊 Trial count set to 5 out of 20');

    if (confirm('Refresh page to see activation button?')) {
        window.location.reload();
    }
}

// Функция для принудительного показа кнопки активации
function forceShowActivationButton() {
    console.log('🔧 Force showing activation button...');

    // Убираем активацию
    localStorage.removeItem('passwordManager_activated');

    // Проверяем текущее состояние
    console.log('📊 Current localStorage state:');
    console.log('- Activated:', localStorage.getItem('passwordManager_activated'));
    console.log('- Trial count:', localStorage.getItem('passwordManager_trialCount'));

    console.log('✅ Button should be visible now');
    console.log('🔄 Refresh page to see the button');

    if (confirm('Refresh page to see activation button?')) {
        window.location.reload();
    }
}

// Простая функция для быстрого тестирования
function quickTestButton() {
    console.log('⚡ Quick test activation button...');
    localStorage.removeItem('passwordManager_activated');
    console.log('✅ Activation removed');
    console.log('🔄 Refreshing page...');
    window.location.reload();
}

// Функция для проверки текущего состояния кнопки
function debugButtonState() {
    console.log('🔍 DEBUG: Current button state');
    console.log('- localStorage activated:', localStorage.getItem('passwordManager_activated'));
    console.log('- Should show button:', localStorage.getItem('passwordManager_activated') !== 'true');

    // Проверяем, есть ли кнопка в DOM
    const buttons = document.querySelectorAll('button');
    const activationButton = Array.from(buttons).find(btn =>
        btn.textContent.includes('Активация') || btn.textContent.includes('ТЕСТ')
    );

    console.log('- Button found in DOM:', !!activationButton);
    if (activationButton) {
        console.log('- Button text:', activationButton.textContent);
        console.log('- Button visible:', activationButton.offsetParent !== null);
    }
}

// Функция для тестирования полного сброса при очистке БД
function testDatabaseClearFullReset() {
    console.log('🧪 Testing database clear with FULL reset...');

    // Сначала активируем программу и устанавливаем счетчик
    localStorage.setItem('passwordManager_activated', 'true');
    localStorage.setItem('passwordManager_trialCount', '15');
    localStorage.setItem('passwordManager_firstRunDate', new Date().toISOString());

    console.log('✅ Step 1: Program activated with trial count');
    console.log('- Activated:', localStorage.getItem('passwordManager_activated'));
    console.log('- Trial count:', localStorage.getItem('passwordManager_trialCount'));
    console.log('- First run date:', !!localStorage.getItem('passwordManager_firstRunDate'));

    // Теперь полностью очищаем БД (имитируем)
    clearAllIncludingActivation();

    console.log('✅ Step 2: Database cleared completely');
    console.log('- Activated:', localStorage.getItem('passwordManager_activated'));
    console.log('- Trial count:', localStorage.getItem('passwordManager_trialCount'));
    console.log('- First run date:', localStorage.getItem('passwordManager_firstRunDate'));

    console.log('🎯 Expected result:');
    console.log('- ALL data should be RESET (null)');
    console.log('- User starts fresh from trial count 0');
    console.log('- User will need to activate from beginning');
}

// Функция для тестирования предупреждений об очистке
function testClearWarnings() {
    console.log('🧪 Testing clear warnings...');

    console.log('📋 Available clear functions with warnings:');
    console.log('1. ClearDataPage - shows detailed warning with license reset info');
    console.log('2. ClearAllDataButton - shows confirm dialog with license warning');
    console.log('3. ClearDatabaseButton - shows confirm dialog with license warning');
    console.log('4. resetAppCompletely() - shows detailed warning');

    console.log('💡 All functions now warn about license key reset');
    console.log('🔑 Users will know they need to re-enter activation key');
}

// Функция для тестирования сохранения счетчика при очистке БД
function testTrialCounterPreservation() {
    console.log('🧪 Testing trial counter preservation after database clear...');

    // Устанавливаем высокий счетчик и активацию
    localStorage.setItem('passwordManager_activated', 'true');
    localStorage.setItem('passwordManager_trialCount', '15');
    localStorage.setItem('passwordManager_firstRunDate', new Date().toISOString());

    console.log('✅ Step 1: Set trial count and activation');
    console.log('- Activated:', localStorage.getItem('passwordManager_activated'));
    console.log('- Trial count:', localStorage.getItem('passwordManager_trialCount'));

    // Имитируем очистку БД (с сохранением счетчика)
    clearWithTrialDataOnly();

    console.log('✅ Step 2: After database clear');
    console.log('- Activated:', localStorage.getItem('passwordManager_activated'));
    console.log('- Trial count:', localStorage.getItem('passwordManager_trialCount'));

    console.log('🎯 Expected result:');
    console.log('- Activation: null (reset)');
    console.log('- Trial count: "15" (preserved)');
    console.log('- Next login will show "Запуск 16 из 20"');
    console.log('- User needs to re-enter activation key');
}

// Функция для проверки видимости кнопки активации
function checkActivationButtonVisibility() {
    console.log('🔍 Checking activation button visibility...');

    const activated = localStorage.getItem('passwordManager_activated') === 'true';
    const trialCount = localStorage.getItem('passwordManager_trialCount') || '0';

    console.log('📊 Current state:');
    console.log('- Activated:', activated);
    console.log('- Trial count:', trialCount);

    if (activated) {
        console.log('🚫 Button is HIDDEN because program is activated');
        console.log('✅ This is correct behavior');
    } else {
        console.log('🟠 Button should be VISIBLE - orange "Активация" button');
        console.log('🎯 Look for orange button with key icon in Dashboard header');
    }

    console.log('💡 Button appears only when program is NOT activated');
}

// Функция для тестирования переключения состояний кнопки
function testButtonStates() {
    console.log('🎭 Testing button state changes...');

    // Показываем кнопку
    localStorage.removeItem('passwordManager_activated');
    console.log('🟠 Step 1: Button should be VISIBLE');
    checkActivationButtonVisibility();

    setTimeout(() => {
        // Скрываем кнопку
        localStorage.setItem('passwordManager_activated', 'true');
        console.log('🚫 Step 2: Button should be HIDDEN');
        checkActivationButtonVisibility();

        console.log('🔄 Refresh page to see the changes');
    }, 2000);
}

// Функция для активации программы
function activateNow() {
    console.log('🔑 Activating program...');
    localStorage.setItem('passwordManager_activated', 'true');
    console.log('✅ Program activated. Reloading...');
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// Делаем функции глобально доступными
window.checkStorageState = checkStorageState;
window.resetApp = resetApp;
window.resetAppCompletely = resetAppCompletely;
window.testPassword = testPassword;
window.fullAuthDiagnostic = fullAuthDiagnostic;
window.createTestPassword = createTestPassword;
window.checkComponentsLoaded = checkComponentsLoaded;
window.forceShowActivation = forceShowActivation;
window.forceTrialExpired = forceTrialExpired;
window.testActivationNow = testActivationNow;
window.testNewActivation = testNewActivation;
window.activateNow = activateNow;
window.setTrialCount = setTrialCount;
window.testActivationUI = testActivationUI;
window.testActivationDialog = testActivationDialog;
window.testCorrectKey = testCorrectKey;
window.testActivationButton = testActivationButton;
// Для обратной совместимости
window.testActivationIcon = testActivationButton;
window.forceShowActivationButton = forceShowActivationButton;
window.checkActivationButtonVisibility = checkActivationButtonVisibility;
window.testButtonStates = testButtonStates;
window.quickTestButton = quickTestButton;
window.debugButtonState = debugButtonState;
window.testDatabaseClearFullReset = testDatabaseClearFullReset;
// Для обратной совместимости
window.testDatabaseClearActivationReset = testDatabaseClearFullReset;
window.testClearWarnings = testClearWarnings;
window.testTableHeaderColors = testTableHeaderColors;
window.testColorPicker = testColorPicker;
window.openTableEditor = openTableEditor;
window.testFullColorPickerPath = testFullColorPickerPath;
window.testTableViewer = testTableViewer;
window.createTestTable = createTestTable;

// Функция для принудительного тестирования просмотра таблицы
function forceTestTableViewer() {
    console.log('🔧 ТЕСТ: Force testing table viewer...');

    // Создаем тестовую таблицу прямо здесь
    const testTable = {
        id: 999,
        name: 'Принудительный тест',
        headers: ['Колонка 1', 'Колонка 2', 'Колонка 3'],
        headerColors: ['#ff6b6b', '#4ecdc4', '#45b7d1'],
        data: [
            ['Данные 1', 'Данные 2', 'Данные 3'],
            ['Тест 1', 'Тест 2', 'Тест 3']
        ],
        createdAt: new Date().toISOString()
    };

    console.log('🔧 ТЕСТ: Test table created:', testTable);

    // Проверяем доступность TableViewer
    if (typeof TableViewer !== 'undefined') {
        console.log('✅ ТЕСТ: TableViewer available, trying to render...');

        // Пытаемся создать элемент напрямую
        try {
            const viewerElement = React.createElement(TableViewer, {
                table: testTable,
                onClose: () => console.log('🔧 ТЕСТ: Viewer closed')
            });
            console.log('✅ ТЕСТ: TableViewer element created successfully');

            // Пытаемся отрендерить в body
            const container = document.createElement('div');
            container.id = 'test-viewer-container';
            document.body.appendChild(container);

            ReactDOM.render(viewerElement, container);
            console.log('✅ ТЕСТ: TableViewer rendered to DOM');

        } catch (error) {
            console.error('❌ ТЕСТ: Error rendering TableViewer:', error);
        }
    } else {
        console.error('❌ ТЕСТ: TableViewer not available');
    }
}

window.forceTestTableViewer = forceTestTableViewer;

// Функция для тестирования просмотра таблиц
function testTableViewer() {
    console.log('👁️ ТЕСТ: Testing table viewer functionality...');
    console.log('📋 ТЕСТ: Instructions:');
    console.log('1. Go to Tables page');
    console.log('2. Look for green eye icon (👁️) on saved tables');
    console.log('3. Click eye icon to open table viewer');
    console.log('4. Try copying individual cells by clicking them');
    console.log('5. Try copying full table with buttons');
    console.log('6. Check for green checkmarks when copying');

    // Проверяем доступность компонентов
    console.log('🔍 Checking components:');
    console.log('- TableViewer available:', typeof TableViewer !== 'undefined');
    console.log('- TableView available:', typeof TableView !== 'undefined');
    console.log('- TablesPage available:', typeof TablesPage !== 'undefined');
    console.log('- createEmptyTable available:', typeof createEmptyTable !== 'undefined');
    console.log('- saveTable available:', typeof saveTable !== 'undefined');
}

// Функция для создания тестовой таблицы
async function createTestTable() {
    console.log('📋 ТЕСТ: Creating test table...');

    try {
        // Создаем тестовую таблицу
        const testTable = createEmptyTable('Тестовая таблица', 3, 4);

        // Заполняем заголовки
        testTable.headers = ['Имя', 'Возраст', 'Город', 'Профессия'];
        testTable.headerColors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4'];

        // Заполняем данные
        testTable.data = [
            ['Иван', '25', 'Москва', 'Программист'],
            ['Мария', '30', 'СПб', 'Дизайнер'],
            ['Петр', '28', 'Казань', 'Менеджер']
        ];

        // Сохраняем таблицу
        const savedId = await saveTable(testTable);
        console.log('✅ ТЕСТ: Test table created with ID:', savedId);
        console.log('💡 ТЕСТ: Go to Tables page to see the test table');

        return testTable;
    } catch (error) {
        console.error('❌ ТЕСТ: Error creating test table:', error);
        return null;
    }
}
window.testTrialCounterPreservation = testTrialCounterPreservation;
// Для обратной совместимости
window.testTrialCounterReset = testTrialCounterPreservation;

// Функция для тестирования цветов заголовков таблиц
function testTableHeaderColors() {
    console.log('🎨 Testing table header colors...');

    // Проверяем доступность функций
    console.log('📋 Available functions:');
    console.log('- createEmptyTable:', typeof createEmptyTable);
    console.log('- updateHeaderColor:', typeof updateHeaderColor);
    console.log('- updateHeader:', typeof updateHeader);

    if (typeof createEmptyTable === 'function') {
        // Создаем тестовую таблицу
        const testTable = createEmptyTable('Тест цветов', 3, 4);
        console.log('✅ Test table created:', testTable);
        console.log('- Headers:', testTable.headers);
        console.log('- Header colors:', testTable.headerColors);

        return testTable;
    } else {
        console.error('❌ createEmptyTable function not available');
        return null;
    }
}

// Функция для быстрого тестирования цветового пикера
function testColorPicker() {
    console.log('🎨 Testing color picker functionality...');
    console.log('📝 Instructions:');
    console.log('1. Go to Tables page');
    console.log('2. Create new table or edit existing');
    console.log('3. Look for RED BACKGROUND color picker under each header');
    console.log('4. Should see "ЦВЕТОВОЙ ПИКЕР" text');
    console.log('5. Change color and see text color change');
    console.log('6. Check browser console for logs');

    // Проверяем доступность компонентов
    console.log('🔍 Checking components:');
    console.log('- TableEditor available:', typeof TableEditor !== 'undefined');
    console.log('- React available:', typeof React !== 'undefined');
    console.log('- createEmptyTable available:', typeof createEmptyTable !== 'undefined');
}

// Функция для принудительного открытия редактора таблиц
function openTableEditor() {
    console.log('🚀 ТЕСТ: Attempting to open table editor...');

    // Шаг 1: Найти кнопку таблиц в хедере
    const tableButton = document.querySelector('button[title="Создание таблицы"]');
    if (tableButton) {
        console.log('✅ ТЕСТ: Found table button in header, clicking...');
        tableButton.click();

        // Ждем немного и ищем кнопку создания таблицы
        setTimeout(() => {
            const createButton = document.querySelector('button:contains("Создать")') ||
                               document.querySelector('button[class*="bg-green"]');
            if (createButton) {
                console.log('✅ ТЕСТ: Found create table button, clicking...');
                createButton.click();
            } else {
                console.log('❌ ТЕСТ: Create table button not found');
                console.log('🔍 ТЕСТ: Available buttons:',
                    Array.from(document.querySelectorAll('button')).map(b => b.textContent || b.title));
            }
        }, 500);
    } else {
        console.log('❌ ТЕСТ: Table button not found in header');
        console.log('🔍 ТЕСТ: Available buttons:',
            Array.from(document.querySelectorAll('button')).map(b => b.textContent || b.title));
    }
}

// Функция для полного тестирования пути к цветовому пикеру
function testFullColorPickerPath() {
    console.log('🎯 ТЕСТ: Full color picker path test');
    console.log('📋 ТЕСТ: Step by step:');
    console.log('1. Click table button in header');
    console.log('2. Wait for TablesPage to load');
    console.log('3. Click create table button');
    console.log('4. Wait for TableEditor to load');
    console.log('5. Look for red color picker blocks');

    openTableEditor();
}

async function checkIndexedDB() {
    try {
        const request = indexedDB.open('passwordManagerDB');
        
        request.onsuccess = async (event) => {
            const db = event.target.result;
            console.log('IndexedDB opened successfully');
            
            if (db.objectStoreNames.contains('settings')) {
                const transaction = db.transaction(['settings'], 'readonly');
                const store = transaction.objectStore('settings');
                const request = store.get('masterPassword');
                
                request.onsuccess = () => {
                    console.log('Master password in IndexedDB:', !!request.result);
                    if (request.result) {
                        console.log('Master password value exists:', !!request.result.value);
                    }
                };
                
                request.onerror = (error) => {
                    console.error('Error reading from IndexedDB:', error);
                };
            } else {
                console.log('Settings store not found in IndexedDB');
            }
        };
        
        request.onerror = (error) => {
            console.error('Error opening IndexedDB:', error);
        };
    } catch (error) {
        console.error('Exception checking IndexedDB:', error);
    }
}

// Запускаем проверку при загрузке страницы
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(checkStorageState, 1000);
});