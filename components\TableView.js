function TableView({ table, onEdit, onDelete, onView }) {
    console.log('🚀 ТЕСТ: TableView props:', { table: table?.name, onEdit: !!onEdit, onDelete: !!onDelete, onView: !!onView });
    const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);

    const handleDelete = async () => {
        try {
            await deleteTable(table.id);
            showToast('Таблица удалена успешно');
            onDelete(table.id);
        } catch (error) {
            console.error('Ошибка удаления таблицы:', error);
            showToast('Ошибка удаления таблицы', 'error');
        }
        setShowDeleteConfirm(false);
    };

    const formatDate = (dateString) => {
        if (!dateString) return 'Неизвестно';
        try {
            return new Date(dateString).toLocaleDateString('ru-RU', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch {
            return 'Неизвестно';
        }
    };

    return (
        <div className="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:border-gray-600 transition-colors">
            <div className="flex justify-between items-start mb-4">
                <div>
                    <h3 className="text-lg font-semibold text-white mb-2">{table.name}</h3>
                    <div className="text-sm text-gray-400 space-y-1">
                        <p>
                            <i className="fas fa-table mr-2"></i>
                            {table.data?.length || 0} строк, {table.headers?.length || 0} колонок
                        </p>
                        <p>
                            <i className="fas fa-calendar mr-2"></i>
                            Создано: {formatDate(table.createdAt)}
                        </p>
                        {table.updatedAt && table.updatedAt !== table.createdAt && (
                            <p>
                                <i className="fas fa-edit mr-2"></i>
                                Изменено: {formatDate(table.updatedAt)}
                            </p>
                        )}
                    </div>
                </div>
                <div className="flex space-x-3">
                    {/* Кнопка просмотра - ЗЕЛЕНЫЙ ГЛАЗ */}
                    <button
                        onClick={() => {
                            console.log('👁️ ТЕСТ: View button clicked!', table?.name);
                            console.log('👁️ ТЕСТ: onView function:', onView);
                            if (onView) {
                                onView(table);
                            } else {
                                console.error('❌ ТЕСТ: onView function not provided!');
                                alert('Функция просмотра не доступна!');
                            }
                        }}
                        className="p-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                        title="👁️ Просмотр таблицы с возможностью копирования"
                    >
                        <i className="fas fa-eye"></i>
                    </button>

                    {/* Кнопка редактирования - СИНИЙ КАРАНДАШ */}
                    <button
                        onClick={() => onEdit(table)}
                        className="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                        title="✏️ Редактировать таблицу"
                    >
                        <i className="fas fa-edit"></i>
                    </button>

                    {/* Кнопка удаления - КРАСНАЯ КОРЗИНА */}
                    <button
                        onClick={() => setShowDeleteConfirm(true)}
                        className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                        title="🗑️ Удалить таблицу"
                    >
                        <i className="fas fa-trash"></i>
                    </button>
                </div>
            </div>

            {/* Превью таблицы */}
            {table.headers && table.data && (
                <div className="overflow-x-auto">
                    <table className="w-full text-sm border-collapse">
                        <thead>
                            <tr>
                                {table.headers.slice(0, 4).map((header, index) => (
                                    <th
                                        key={index}
                                        className="p-2 bg-gray-700 border border-gray-600 text-left font-medium"
                                        style={{ color: table.headerColors?.[index] || '#d1d5db' }}
                                    >
                                        {header || `Колонка ${index + 1}`}
                                    </th>
                                ))}
                                {table.headers.length > 4 && (
                                    <th className="p-2 bg-gray-700 border border-gray-600 text-center text-gray-400">
                                        +{table.headers.length - 4}
                                    </th>
                                )}
                            </tr>
                        </thead>
                        <tbody>
                            {table.data.slice(0, 3).map((row, rowIndex) => (
                                <tr key={rowIndex}>
                                    {row.slice(0, 4).map((cell, colIndex) => (
                                        <td key={colIndex} className="p-2 border border-gray-600 text-gray-300 max-w-32 truncate">
                                            {cell || '-'}
                                        </td>
                                    ))}
                                    {row.length > 4 && (
                                        <td className="p-2 border border-gray-600 text-center text-gray-400">
                                            ...
                                        </td>
                                    )}
                                </tr>
                            ))}
                            {table.data.length > 3 && (
                                <tr>
                                    <td colSpan={Math.min(table.headers.length, 5)} className="p-2 border border-gray-600 text-center text-gray-400">
                                        +{table.data.length - 3} строк
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            )}

            {/* Диалог подтверждения удаления */}
            {showDeleteConfirm && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-gray-800 border border-gray-700 rounded-xl p-6 max-w-md w-full mx-4">
                        <div className="text-center mb-6">
                            <i className="fas fa-exclamation-triangle text-4xl text-yellow-400 mb-4"></i>
                            <h3 className="text-lg font-bold text-white mb-2">Удалить таблицу?</h3>
                            <p className="text-gray-400">
                                Вы уверены, что хотите удалить таблицу "{table.name}"? 
                                Это действие нельзя отменить.
                            </p>
                        </div>
                        <div className="flex space-x-3">
                            <button
                                onClick={handleDelete}
                                className="flex-1 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-colors"
                            >
                                Удалить
                            </button>
                            <button
                                onClick={() => setShowDeleteConfirm(false)}
                                className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-semibold transition-colors"
                            >
                                Отмена
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
