function ActivationIcon({ onShowActivationDialog }) {
    try {
        const [isActivated, setIsActivated] = React.useState(false);

        // Проверяем статус активации при монтировании
        React.useEffect(() => {
            const checkActivationStatus = () => {
                const activated = localStorage.getItem('passwordManager_activated') === 'true';
                console.log('🔍 ActivationIcon: Checking activation status:', activated);
                setIsActivated(activated);
            };

            console.log('🔄 ActivationIcon: Component mounted');
            checkActivationStatus();

            // Проверяем статус каждые 5 секунд (на случай активации в другой вкладке)
            const interval = setInterval(checkActivationStatus, 5000);

            return () => {
                console.log('🔄 ActivationIcon: Component unmounted');
                clearInterval(interval);
            };
        }, []);

        // Если программа активирована, не показываем иконку
        if (isActivated) {
            console.log('🚫 ActivationIcon: Program is activated, hiding icon');
            return null;
        }

        console.log('✅ ActivationIcon: Program not activated, showing icon');

        const handleClick = () => {
            console.log('🔑 Activation icon clicked');
            if (onShowActivationDialog) {
                onShowActivationDialog();
            }
        };

        return (
            <button
                onClick={handleClick}
                className="text-orange-400 hover:text-orange-300 transition-colors"
                title="Активировать программу"
                data-name="activation-icon"
                data-file="components/ActivationIcon.js"
            >
                <i className="fas fa-unlock-alt"></i>
            </button>
        );
    } catch (error) {
        console.error('ActivationIcon component error:', error);
        if (typeof reportError === 'function') {
            reportError(error);
        }
        return null;
    }
}
