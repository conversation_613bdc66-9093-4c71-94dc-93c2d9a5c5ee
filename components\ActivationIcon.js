function ActivationIcon({ onShowActivationDialog }) {
    try {
        const [isActivated, setIsActivated] = React.useState(false);

        // Проверяем статус активации при монтировании
        React.useEffect(() => {
            const checkActivationStatus = () => {
                const activated = localStorage.getItem('passwordManager_activated') === 'true';
                console.log('🔍 ActivationIcon: Checking activation status:', activated);
                setIsActivated(activated);
            };

            console.log('🔄 ActivationIcon: Component mounted');
            checkActivationStatus();

            // Проверяем статус каждые 5 секунд (на случай активации в другой вкладке)
            const interval = setInterval(checkActivationStatus, 5000);

            return () => {
                console.log('🔄 ActivationIcon: Component unmounted');
                clearInterval(interval);
            };
        }, []);

        console.log('✅ ActivationIcon: Always showing icon, state:', isActivated ? 'INACTIVE' : 'ACTIVE');

        const handleClick = () => {
            if (isActivated) {
                console.log('🔒 Activation icon clicked but program is already activated');
                return;
            }
            console.log('🔑 Activation icon clicked');
            if (onShowActivationDialog) {
                onShowActivationDialog();
            }
        };

        return (
            <button
                onClick={handleClick}
                className={`transition-colors ${
                    isActivated
                        ? 'text-gray-600 cursor-not-allowed'
                        : 'text-orange-400 hover:text-orange-300'
                }`}
                title={isActivated ? "Программа активирована" : "Активировать программу"}
                disabled={isActivated}
                data-name="activation-icon"
                data-file="components/ActivationIcon.js"
            >
                <i className={`fas ${isActivated ? 'fa-lock' : 'fa-unlock-alt'}`}></i>
            </button>
        );
    } catch (error) {
        console.error('ActivationIcon component error:', error);
        if (typeof reportError === 'function') {
            reportError(error);
        }
        return null;
    }
}
