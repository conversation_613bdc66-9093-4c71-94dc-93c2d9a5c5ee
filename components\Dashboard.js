function Dashboard({ onLogout, onResetPassword }) {
    try {
        const [passwords, setPasswords] = React.useState([]);
        const [cards, setCards] = React.useState([]);
        // Удалено состояние для заметок
        const [tabs, setTabs] = React.useState([]);
        const [activeTab, setActiveTab] = React.useState('Все');
        const [showAddForm, setShowAddForm] = React.useState(false);
        const [editingPassword, setEditingPassword] = React.useState(null);
        const [showCardForm, setShowCardForm] = React.useState(false);
        const [showNotesPage, setShowNotesPage] = React.useState(false);
        const [showDocumentsPage, setShowDocumentsPage] = React.useState(false);
        const [showAboutAuthor, setShowAboutAuthor] = React.useState(false);
        const [showTabManager, setShowTabManager] = React.useState(false);
        const [showActivationDialog, setShowActivationDialog] = React.useState(false);
        const [isActivated, setIsActivated] = React.useState(false);

        const [showClearDataPage, setShowClearDataPage] = React.useState(false);
        const [showTablesPage, setShowTablesPage] = React.useState(false);
        // Удалено поле поиска
        const [loading, setLoading] = React.useState(true);

        React.useEffect(() => {
            initializeDashboard();

            // Проверяем статус активации
            const checkActivationStatus = () => {
                const activated = localStorage.getItem('passwordManager_activated') === 'true';
                console.log('🔍 Dashboard: Checking activation status:', activated);
                setIsActivated(activated);
            };

            checkActivationStatus();

            // Проверяем статус каждые 5 секунд
            const interval = setInterval(checkActivationStatus, 5000);

            return () => clearInterval(interval);
        }, []);

        React.useEffect(() => {
            if (tabs.length > 0) {
                loadData();
            }
        }, [activeTab, tabs]);

        const initializeDashboard = async () => {
            try {
                await loadTabs();
                // Устанавливаем "Банковские карты" как активную вкладку по умолчанию
                setActiveTab('Банковские карты');
            } catch (error) {
                console.error('Dashboard initialization error:', error);
                showToast('Failed to initialize dashboard', 'error');
            } finally {
                setLoading(false);
            }
        };

        const loadTabs = async () => {
            try {
                const savedTabs = await getTabs();
                
                // Сортируем вкладки, чтобы "Банковские карты" была первой
                const sortedTabs = [...savedTabs].sort((a, b) => {
                    if (a.name === 'Банковские карты') return -1;
                    if (b.name === 'Банковские карты') return 1;
                    return 0;
                });
                
                // Фильтруем вкладки "Заметки" и "Документы", если они есть
                const filteredTabs = sortedTabs.filter(tab => tab.name !== 'Заметки' && tab.name !== 'Документы');
                
                setTabs(filteredTabs);
                
                if (filteredTabs.length > 0 && !filteredTabs.find(tab => tab.name === activeTab) && activeTab !== 'Заметки') {
                    setActiveTab(filteredTabs[0].name);
                }
            } catch (error) {
                console.error('Load tabs error:', error);
                showToast('Failed to load tabs', 'error');
            }
        };

        const loadData = async () => {
            try {
                if (activeTab === 'Банковские карты') {
                    const savedCards = await getCards();
                    setCards(savedCards);
                    setPasswords([]);
                } else {
                    const savedPasswords = await getPasswords(activeTab);
                    setPasswords(savedPasswords);
                    setCards([]);
                }
            } catch (error) {
                console.error('Load data error:', error);
                showToast('Failed to load data', 'error');
            }
        };

        const handleAddPassword = () => {
            setShowAddForm(false);
            setEditingPassword(null);
            loadData();
        };

        const handleAddCard = () => {
            setShowCardForm(false);
            loadData();
        };
        
        // Удалены функции для работы с заметками, так как они перенесены в NotesPage

        const handleDeletePassword = async (id) => {
            try {
                await deletePassword(id);
                showToast('Password deleted successfully');
                await loadData();
                return true;
            } catch (error) {
                console.error('Delete password error:', error);
                showToast('Failed to delete password', 'error');
                throw error;
            }
        };

        const handleDeleteCard = async (id) => {
            try {
                await deleteCard(id);
                showToast('Card deleted successfully');
                await loadData();
                return true;
            } catch (error) {
                console.error('Delete card error:', error);
                showToast('Failed to delete card', 'error');
                throw error;
            }
        };

        const handleTabsUpdate = () => {
            loadTabs();
        };

        const handleTabClick = (tabName) => {
            setActiveTab(tabName);
        };
        
        const handleEditPassword = (password) => {
            setEditingPassword(password);
            setShowAddForm(true);
        };

        const handleShowClearDataPage = () => {
            setShowClearDataPage(true);
        };

        const handleShowActivationDialog = () => {
            console.log('🔑 Dashboard: Showing activation dialog');
            setShowActivationDialog(true);
        };

        const handleActivation = async (key) => {
            const correctKey = 'neirotopchik_vk_2000_500-7000!!!!!';
            console.log('🔑 Dashboard: Attempting activation');

            if (key === correctKey) {
                localStorage.setItem('passwordManager_activated', 'true');
                setIsActivated(true); // Обновляем состояние сразу
                setShowActivationDialog(false);
                console.log('🎉 Dashboard: Program activated successfully!');
                showToast('Программа успешно активирована!', 'success');
                return true;
            }
            console.log('❌ Dashboard: Activation failed - incorrect key');
            return false;
        };

        const handleCancelActivation = () => {
            console.log('👤 Dashboard: User cancelled activation');
            setShowActivationDialog(false);
        };

        const handleCloseClearDataPage = () => {
            setShowClearDataPage(false);
        };

        const isCardsTab = activeTab === 'Банковские карты';
        let filteredData = isCardsTab ? cards : passwords;

        // Если показываем страницу очистки данных, рендерим только её
        if (showClearDataPage) {
            return (
                <ClearDataPage
                    onClose={handleCloseClearDataPage}
                />
            );
        }

        // Если показываем страницу таблиц, рендерим только её
        if (showTablesPage) {
            return (
                <TablesPage
                    onClose={() => setShowTablesPage(false)}
                />
            );
        }

        return (
            <div className="min-h-screen bg-gray-900" data-name="dashboard" data-file="components/Dashboard.js">
                <header className="glass-effect border-b border-gray-700 px-6 py-4">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                            <i className="fas fa-shield-alt text-2xl text-blue-400"></i>
                            <h1 className="text-xl font-bold">Password Manager</h1>
                        </div>
                        <div className="flex items-center space-x-4">
                            <button
                                onClick={() => {
                                    if (isCardsTab) {
                                        setShowCardForm(true);
                                    } else {
                                        setShowAddForm(true);
                                    }
                                }}
                                className="text-gray-400 hover:text-white transition-colors"
                                title={isCardsTab ? "Добавить карту" : "Добавить пароль"}
                            >
                                <i className="fas fa-plus"></i>
                            </button>
                            <button
                                onClick={() => setShowNotesPage(true)}
                                className="text-gray-400 hover:text-white transition-colors"
                                title="Заметки"
                            >
                                <i className="fas fa-sticky-note"></i>
                            </button>
                            <button
                                onClick={() => setShowDocumentsPage(true)}
                                className="text-gray-400 hover:text-white transition-colors"
                                title="Документы"
                            >
                                <i className="fas fa-id-card"></i>
                            </button>
                            <button
                                onClick={() => setShowTablesPage(true)}
                                className="text-gray-400 hover:text-white transition-colors"
                                title="Создание таблицы"
                            >
                                <i className="fas fa-table"></i>
                            </button>
                            <button
                                onClick={() => setShowAboutAuthor(true)}
                                className="text-gray-400 hover:text-white transition-colors"
                                title="Об авторе"
                            >
                                <i className="fas fa-info-circle"></i>
                            </button>
                            {!isActivated && (
                                <button
                                    onClick={handleShowActivationDialog}
                                    className="text-orange-400 hover:text-orange-300 transition-colors"
                                    title="Активировать программу"
                                >
                                    <i className="fas fa-unlock-alt"></i>
                                </button>
                            )}
                            <button
                                onClick={onResetPassword}
                                className="text-gray-400 hover:text-white transition-colors"
                                title="Сбросить пароль"
                            >
                                <i className="fas fa-key"></i>
                            </button>
                            <ClearDatabaseIcon onShowClearDataPage={handleShowClearDataPage} />
                            <button
                                onClick={onLogout}
                                className="text-gray-400 hover:text-white transition-colors"
                                title="Выйти"
                            >
                                <i className="fas fa-sign-out-alt"></i>
                            </button>
                        </div>
                    </div>
                </header>

                <div className="px-6 py-4 border-b border-gray-700 overflow-x-auto">
                    <div className="flex items-center justify-between">
                        <DraggableTabs 
                            tabs={tabs} 
                            activeTab={activeTab} 
                            onTabClick={handleTabClick} 
                            onTabsReorder={async (reorderedTabs) => {
                                try {
                                    // Очищаем хранилище вкладок
                                    const transaction = db.transaction(['tabs'], 'readwrite');
                                    const store = transaction.objectStore('tabs');
                                    await new Promise((resolve) => {
                                        const request = store.clear();
                                        request.onsuccess = () => resolve();
                                    });
                                    
                                    // Добавляем вкладки в новом порядке
                                    for (const tab of reorderedTabs) {
                                        await new Promise((resolve) => {
                                            const request = store.add({
                                                name: tab.name,
                                                isDefault: tab.isDefault,
                                                type: tab.type
                                            });
                                            request.onsuccess = () => resolve();
                                        });
                                    }
                                    
                                    // Обновляем список вкладок
                                    loadTabs();
                                } catch (error) {
                                    console.error('Error reordering tabs:', error);
                                }
                            }}
                        />
                        <button
                            onClick={() => setShowTabManager(true)}
                            className="ml-2 px-4 py-2 bg-gray-700 text-gray-300 hover:bg-gray-600 rounded-lg font-medium transition-colors whitespace-nowrap"
                        >
                            Add Tab
                        </button>
                    </div>
                </div>

                <main className="p-6">
                    <div className="max-w-6xl mx-auto">

                        {loading ? (
                            <div className="text-center py-12">
                                <i className="fas fa-spinner fa-spin text-2xl text-blue-400"></i>
                                <p className="mt-4 text-gray-400">Loading data...</p>
                            </div>
                        ) : filteredData.length === 0 ? (
                            <div className="text-center py-12">
                                <i className={`fas ${isCardsTab ? 'fa-credit-card' : 'fa-key'} text-4xl text-gray-600 mb-4`}></i>
                                <p className="text-gray-400">
                                    {isCardsTab ? 'Нет карт' : `Нет паролей в категории ${activeTab}`}
                                </p>
                            </div>
                        ) : (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {filteredData.map(item => 
                                    isCardsTab ? (
                                        <CardView
                                            key={item.id}
                                            card={item}
                                            onDelete={handleDeleteCard}
                                        />
                                    ) : (
                                        <PasswordCard
                                            key={item.id}
                                            password={item}
                                            onDelete={handleDeletePassword}
                                            onEdit={handleEditPassword}
                                        />
                                    )
                                )}
                            </div>
                        )}
                    </div>
                </main>

                {showAddForm && (
                    <AddPasswordForm
                        tabs={tabs}
                        onAdd={handleAddPassword}
                        onCancel={() => {
                            setShowAddForm(false);
                            setEditingPassword(null);
                        }}
                        editPassword={editingPassword}
                    />
                )}

                {showCardForm && (
                    <CardForm
                        onAdd={handleAddCard}
                        onCancel={() => setShowCardForm(false)}
                    />
                )}

                {showTabManager && (
                    <TabManager
                        tabs={tabs}
                        onTabsUpdate={handleTabsUpdate}
                        onClose={() => setShowTabManager(false)}
                    />
                )}
                
                {showNotesPage && (
                    <NotesPage
                        onClose={() => setShowNotesPage(false)}
                    />
                )}
                
                {showDocumentsPage && (
                    <DocumentsPage
                        onClose={() => setShowDocumentsPage(false)}
                    />
                )}
                
                {showAboutAuthor && (
                    <AboutAuthor
                        onClose={() => setShowAboutAuthor(false)}
                    />
                )}

                {showActivationDialog && (
                    <ActivationDialog
                        onActivate={handleActivation}
                        onCancel={handleCancelActivation}
                        isTrialExpired={false}
                        trialCount={0}
                        remainingRuns={0}
                    />
                )}
            </div>
        );
    } catch (error) {
        console.error('Dashboard component error:', error);
        reportError(error);
    }
}
