<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Manager - Secure Password Storage</title>
    <meta name="description" content="Secure password manager with dark theme design">
    <meta name="keywords" content="password manager, security, encryption, dark theme">
    <meta property="og:title" content="Password Manager">
    <meta property="og:description" content="Secure password manager with dark theme design">
    <meta name="twitter:title" content="Password Manager">
    <meta name="twitter:description" content="Secure password manager with dark theme design">
    <script src="https://resource.trickle.so/vendor_lib/unpkg/react@18/umd/react.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <script>
        // Очистка IndexedDB при необходимости отладки
        // Раскомментируйте следующий код для сброса базы данных
        /*
        window.addEventListener('load', function() {
            const request = indexedDB.deleteDatabase('passwordManagerDB');
            request.onsuccess = function() {
                console.log('Database deleted successfully');
                window.location.reload();
            };
            request.onerror = function() {
                console.error('Error deleting database');
            };
        });
        */
    </script>
</head>
<body class="bg-gray-900 text-white">
    <div id="root"></div>
    <!-- Сначала загружаем утилиты -->
    <script type="text/babel" src="utils/notifications.js"></script>
    <script type="text/babel" src="utils/crypto.js"></script>
    <script type="text/babel" src="utils/database.js"></script>
    <script type="text/babel" src="utils/storage.js"></script>
    <script type="text/babel" src="utils/notes.js"></script>
    <script type="text/babel" src="utils/documents.js"></script>
    <script type="text/babel" src="utils/tables.js"></script>
    <script type="text/babel" src="utils/activation.js"></script>
    <script type="text/babel" src="utils/safeClear.js"></script>
    <!-- Затем вспомогательные скрипты -->
    <script type="text/babel" src="debug.js"></script>
    <script type="text/babel" src="reset-db-version.js"></script>
    <script type="text/babel" src="fix-db-init.js"></script>
    <script type="text/babel" src="fix-password.js"></script>
    <script type="text/babel" src="fix-tabs-order.js"></script>
    <script type="text/babel" src="fix-notes-tab.js"></script>
    <script type="text/babel" src="components/SessionManager.js"></script>
    <script type="text/babel" src="components/DbClearedNotification.js"></script>
    <script type="text/babel" src="components/ActivationDialog.js"></script>
    <script type="text/babel" src="components/TrialNotification.js"></script>
    <script type="text/babel" src="components/ActivationStatus.js"></script>
    <script type="text/babel" src="components/ActivationManager.js"></script>
    <script type="text/babel" src="components/ActivationIcon.js"></script>
    <!-- Затем компоненты -->
    <script type="text/babel" src="components/LoginForm.js"></script>
    <script type="text/babel" src="components/ResetPasswordForm.js"></script>
    <script type="text/babel" src="components/PasswordCard.js"></script>
    <script type="text/babel" src="components/AddPasswordForm.js"></script>
    <script type="text/babel" src="components/CardForm.js"></script>
    <script type="text/babel" src="components/CardView.js"></script>
    <script type="text/babel" src="components/DraggableTabs.js"></script>
    <script type="text/babel" src="components/TabManager.js"></script>
    <script type="text/babel" src="components/ClearDataConfirmation.js"></script>
    <script type="text/babel" src="components/ClearDatabaseIcon.js"></script>
    <script type="text/babel" src="components/AboutAuthor.js"></script>
    <script type="text/babel" src="components/ImageViewer.js"></script>
    <script type="text/babel" src="components/ClearDataPage.js"></script>
    <script type="text/babel" src="components/ClearAllDataButton.js"></script>
    <script type="text/babel" src="components/NoteForm.js"></script>
    <script type="text/babel" src="components/NoteView.js"></script>
    <script type="text/babel" src="components/NotesPage.js"></script>
    <script type="text/babel" src="components/DocumentForm.js"></script>
    <script type="text/babel" src="components/DocumentEditForm.js"></script>
    <script type="text/babel" src="components/DocumentView.js"></script>
    <script type="text/babel" src="components/DocumentsPage.js"></script>
    <script type="text/babel" src="components/TableView.js"></script>
    <script type="text/babel" src="components/TableEditor.js"></script>
    <script type="text/babel" src="components/TablesPage.js"></script>
    <script type="text/babel" src="components/Dashboard.js"></script>
    <!-- Основное приложение загружаем последним -->
    <script type="text/babel" src="app.js"></script>
</body>
</html>
