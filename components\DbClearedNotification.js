function DbClearedNotification() {
    React.useEffect(() => {
        // Проверяем, была ли очищена база данных
        const wasCleared = sessionStorage.getItem('dbCleared') === 'true';
        
        if (wasCleared) {
            // Удаляем флаг
            sessionStorage.removeItem('dbCleared');
            
            // Показываем уведомление
            setTimeout(() => {
                showToast('База данных успешно очищена');
            }, 500);
        }
    }, []);
    
    return null; // Компонент не отображает никакого UI
}