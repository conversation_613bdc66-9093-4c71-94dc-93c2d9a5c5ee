function TablesPage({ onClose }) {
    console.log('🚀 ТЕСТ: TablesPage component loaded!');
    const [tables, setTables] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [showEditor, setShowEditor] = React.useState(false);
    const [editingTable, setEditingTable] = React.useState(null);
    const [searchQuery, setSearchQuery] = React.useState('');

    React.useEffect(() => {
        loadTables();
    }, []);

    const loadTables = async () => {
        try {
            setLoading(true);
            const savedTables = await getTables();
            setTables(savedTables);
        } catch (error) {
            console.error('Ошибка загрузки таблиц:', error);
            showToast('Ошибка загрузки таблиц', 'error');
        } finally {
            setLoading(false);
        }
    };

    const handleCreateTable = () => {
        console.log('🚀 ТЕСТ: Create table button clicked!');
        setEditingTable(null);
        setShowEditor(true);
        console.log('🚀 ТЕСТ: setShowEditor(true) called');
    };

    const handleEditTable = (table) => {
        setEditingTable(table);
        setShowEditor(true);
    };

    const handleSaveTable = () => {
        setShowEditor(false);
        setEditingTable(null);
        loadTables();
    };

    const handleDeleteTable = (tableId) => {
        setTables(tables.filter(table => table.id !== tableId));
    };

    const filteredTables = tables.filter(table =>
        table.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    if (showEditor) {
        console.log('🚀 ТЕСТ: Rendering TableEditor!');
        console.log('🚀 ТЕСТ: editingTable:', editingTable);
        return (
            <TableEditor
                table={editingTable}
                onSave={handleSaveTable}
                onCancel={() => {
                    console.log('🚀 ТЕСТ: TableEditor cancel clicked');
                    setShowEditor(false);
                    setEditingTable(null);
                }}
            />
        );
    }

    return (
        <div className="fixed inset-0 bg-gray-900 z-50">
            <header className="bg-gray-800 border-b border-gray-700 px-6 py-4">
                <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-3">
                        <i className="fas fa-table text-2xl text-green-400"></i>
                        <h1 className="text-xl font-bold">Таблицы</h1>
                    </div>
                    <div className="flex items-center space-x-4">
                        <button
                            onClick={handleCreateTable}
                            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
                        >
                            <i className="fas fa-plus mr-2"></i>Создать таблицу
                        </button>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-white transition-colors"
                            title="Закрыть"
                        >
                            <i className="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </header>

            <div className="p-6">
                <div className="max-w-6xl mx-auto">
                    {/* Поиск */}
                    <div className="mb-6">
                        <div className="relative max-w-md">
                            <input
                                type="text"
                                placeholder="Поиск таблиц..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full px-4 py-2 pl-10 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500"
                            />
                            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                    </div>

                    {/* Содержимое */}
                    {loading ? (
                        <div className="text-center py-12">
                            <i className="fas fa-spinner fa-spin text-2xl text-green-400"></i>
                            <p className="mt-4 text-gray-400">Загрузка таблиц...</p>
                        </div>
                    ) : filteredTables.length === 0 ? (
                        <div className="text-center py-12">
                            <i className="fas fa-table text-4xl text-gray-600 mb-4"></i>
                            <p className="text-gray-400 mb-4">
                                {searchQuery ? 'Таблицы не найдены' : 'У вас пока нет таблиц'}
                            </p>
                            {!searchQuery && (
                                <button
                                    onClick={handleCreateTable}
                                    className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
                                >
                                    <i className="fas fa-plus mr-2"></i>Создать первую таблицу
                                </button>
                            )}
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {filteredTables.map(table => (
                                <TableView
                                    key={table.id}
                                    table={table}
                                    onEdit={handleEditTable}
                                    onDelete={handleDeleteTable}
                                />
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
