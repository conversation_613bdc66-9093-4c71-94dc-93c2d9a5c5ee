function ActivationDialog({ onActivate, onCancel, isTrialExpired = false, trialCount = 0, remainingRuns = 0 }) {
    try {
        const [activationKey, setActivationKey] = React.useState('');
        const [loading, setLoading] = React.useState(false);
        const [error, setError] = React.useState('');

        const handleSubmit = async (e) => {
            e.preventDefault();
            setLoading(true);
            setError('');

            try {
                if (!activationKey.trim()) {
                    setError('Введите ключ активации');
                    return;
                }

                console.log('🔑 Attempting activation with key:', activationKey.trim());
                const success = await onActivate(activationKey.trim());
                console.log('🔑 Activation result:', success);

                if (!success) {
                    setError('Неверный ключ активации');
                }
            } catch (error) {
                console.error('Activation error:', error);
                setError('Произошла ошибка при активации');
            } finally {
                setLoading(false);
            }
        };

        return (
            <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-[9999]" data-name="activation-dialog" data-file="components/ActivationDialog.js">
                <div className="glass-effect rounded-2xl p-8 w-full max-w-md">
                    <div className="text-center mb-8">
                        <i className="fas fa-key text-4xl text-yellow-400 mb-4"></i>
                        <h1 className="text-2xl font-bold mb-2">
                            {isTrialExpired ? 'Активация требуется' : 'Активация программы'}
                        </h1>
                        <p className="text-gray-400">
                            {isTrialExpired 
                                ? 'Пробный период истек. Для продолжения работы необходимо активировать программу.'
                                : 'Введите ключ активации для полной версии программы'
                            }
                        </p>
                    </div>

                    {!isTrialExpired && (
                        <div className="bg-blue-900 bg-opacity-50 rounded-lg p-4 mb-6">
                            <div className="flex items-center mb-2">
                                <i className="fas fa-info-circle text-blue-400 mr-2"></i>
                                <span className="font-semibold text-blue-400">Информация о демоверсии</span>
                            </div>
                            <p className="text-sm text-gray-300">
                                Запуск {trialCount} из 10
                            </p>
                            <p className="text-sm text-gray-300">
                                Осталось запусков: {remainingRuns}
                            </p>
                        </div>
                    )}

                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div>
                            <label className="block text-sm font-medium mb-2">
                                Ключ активации
                            </label>
                            <input
                                type="text"
                                value={activationKey}
                                onChange={(e) => setActivationKey(e.target.value)}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="Введите ключ активации"
                                autoFocus
                            />
                            {error && (
                                <p className="text-red-400 text-sm mt-2">{error}</p>
                            )}
                        </div>

                        <div className="flex space-x-4">
                            {!isTrialExpired && (
                                <button
                                    type="button"
                                    onClick={onCancel}
                                    className="w-1/2 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold transition-colors"
                                >
                                    Позже
                                </button>
                            )}
                            <button
                                type="submit"
                                disabled={loading}
                                className={`${isTrialExpired ? 'w-full' : 'w-1/2'} py-3 btn-primary rounded-lg font-semibold disabled:opacity-50 transition-colors`}
                            >
                                {loading ? 'Активация...' : 'Активировать'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    } catch (error) {
        console.error('ActivationDialog component error:', error);
        if (typeof reportError === 'function') {
            reportError(error);
        }
        return (
            <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-50">
                <div className="glass-effect rounded-2xl p-8 w-full max-w-md text-center">
                    <i className="fas fa-exclamation-triangle text-4xl text-red-400 mb-4"></i>
                    <h2 className="text-xl font-bold mb-2">Ошибка</h2>
                    <p className="text-gray-400">Произошла ошибка при загрузке диалога активации</p>
                </div>
            </div>
        );
    }
}
