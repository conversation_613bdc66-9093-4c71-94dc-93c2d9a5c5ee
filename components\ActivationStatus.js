function ActivationStatus({ onActivate }) {
    try {
        const [activationStatus, setActivationStatus] = React.useState(null);

        React.useEffect(() => {
            const status = getActivationStatus();
            setActivationStatus(status);
        }, []);

        if (!activationStatus || activationStatus.activated) {
            return null; // Не показываем, если активировано
        }

        return (
            <div className="bg-orange-900 bg-opacity-30 border border-orange-700 rounded-lg p-3 mb-4" data-name="activation-status" data-file="components/ActivationStatus.js">
                <div className="flex items-center justify-between">
                    <div className="flex items-center">
                        <i className="fas fa-clock text-orange-400 mr-2"></i>
                        <div>
                            <span className="text-sm font-medium text-orange-400">
                                Демоверсия
                            </span>
                            <div className="text-xs text-orange-300">
                                Запуск {activationStatus.trialCount} из {activationStatus.maxTrialRuns}
                                {activationStatus.remainingRuns > 0 && (
                                    <span> • Осталось: {activationStatus.remainingRuns}</span>
                                )}
                            </div>
                        </div>
                    </div>
                    
                    <button
                        onClick={onActivate}
                        className="px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs rounded-md transition-colors"
                    >
                        <i className="fas fa-key mr-1"></i>
                        Активировать
                    </button>
                </div>
                
                {/* Прогресс-бар */}
                <div className="mt-2">
                    <div className="w-full bg-orange-900 rounded-full h-1">
                        <div 
                            className="bg-orange-400 h-1 rounded-full transition-all duration-300"
                            style={{ 
                                width: `${(activationStatus.trialCount / activationStatus.maxTrialRuns) * 100}%` 
                            }}
                        ></div>
                    </div>
                </div>
                
                {activationStatus.trialExpired && (
                    <div className="mt-2 text-xs text-red-400">
                        <i className="fas fa-exclamation-triangle mr-1"></i>
                        Пробный период истек. Требуется активация.
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('ActivationStatus component error:', error);
        if (typeof reportError === 'function') {
            reportError(error);
        }
        return null;
    }
}
