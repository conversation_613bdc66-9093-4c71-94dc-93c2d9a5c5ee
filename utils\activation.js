// Утилиты для управления активацией приложения

// Правильный ключ активации
const ACTIVATION_KEY = 'neirotopchik_vk_2000_500-7000!!!!!';

// Максимальное количество запусков демоверсии
const MAX_TRIAL_RUNS = 10;

// Ключи для localStorage
const STORAGE_KEYS = {
    ACTIVATION_STATUS: 'passwordManager_activated',
    TRIAL_COUNT: 'passwordManager_trialCount',
    FIRST_RUN_DATE: 'passwordManager_firstRunDate'
};

/**
 * Проверяет, активирована ли программа
 * @returns {boolean} true если программа активирована
 */
function isActivated() {
    return localStorage.getItem(STORAGE_KEYS.ACTIVATION_STATUS) === 'true';
}

/**
 * Активирует программу с помощью ключа
 * @param {string} key - ключ активации
 * @returns {boolean} true если ключ правильный и активация прошла успешно
 */
function activateProgram(key) {
    if (key === ACTIVATION_KEY) {
        localStorage.setItem(STORAGE_KEYS.ACTIVATION_STATUS, 'true');
        showToast('Программа успешно активирована!', 'success');
        return true;
    }
    return false;
}

/**
 * Получает количество запусков демоверсии
 * @returns {number} количество запусков
 */
function getTrialCount() {
    const count = localStorage.getItem(STORAGE_KEYS.TRIAL_COUNT);
    return count ? parseInt(count, 10) : 0;
}

/**
 * Увеличивает счетчик запусков демоверсии
 * @returns {number} новое количество запусков
 */
function incrementTrialCount() {
    const currentCount = getTrialCount();
    const newCount = currentCount + 1;
    localStorage.setItem(STORAGE_KEYS.TRIAL_COUNT, newCount.toString());
    
    // Сохраняем дату первого запуска
    if (currentCount === 0) {
        localStorage.setItem(STORAGE_KEYS.FIRST_RUN_DATE, new Date().toISOString());
    }
    
    return newCount;
}

/**
 * Проверяет, можно ли запустить демоверсию
 * @returns {boolean} true если можно запустить демоверсию
 */
function canRunTrial() {
    return getTrialCount() < MAX_TRIAL_RUNS;
}

/**
 * Проверяет, истек ли пробный период
 * @returns {boolean} true если пробный период истек
 */
function isTrialExpired() {
    return getTrialCount() >= MAX_TRIAL_RUNS;
}

/**
 * Получает оставшееся количество запусков демоверсии
 * @returns {number} количество оставшихся запусков
 */
function getRemainingTrialRuns() {
    return Math.max(0, MAX_TRIAL_RUNS - getTrialCount());
}

/**
 * Получает дату первого запуска
 * @returns {Date|null} дата первого запуска или null если не найдена
 */
function getFirstRunDate() {
    const dateStr = localStorage.getItem(STORAGE_KEYS.FIRST_RUN_DATE);
    return dateStr ? new Date(dateStr) : null;
}

/**
 * Сбрасывает данные активации (для отладки)
 */
function resetActivationData() {
    localStorage.removeItem(STORAGE_KEYS.ACTIVATION_STATUS);
    localStorage.removeItem(STORAGE_KEYS.TRIAL_COUNT);
    localStorage.removeItem(STORAGE_KEYS.FIRST_RUN_DATE);
    console.log('Activation data reset');
}

/**
 * Функция для отладки - выводит текущий статус активации в консоль
 */
function debugActivationStatus() {
    const status = getActivationStatus();
    console.log('=== Activation Debug Info ===');
    console.log('Activated:', status.activated);
    console.log('Trial Count:', status.trialCount);
    console.log('Remaining Runs:', status.remainingRuns);
    console.log('Trial Expired:', status.trialExpired);
    console.log('First Run Date:', status.firstRunDate);
    console.log('Max Trial Runs:', status.maxTrialRuns);
    console.log('============================');
    return status;
}

/**
 * Получает статус активации для отображения
 * @returns {object} объект с информацией о статусе
 */
function getActivationStatus() {
    const activated = isActivated();
    const trialCount = getTrialCount();
    const remainingRuns = getRemainingTrialRuns();
    const trialExpired = isTrialExpired();
    const firstRunDate = getFirstRunDate();
    
    return {
        activated,
        trialCount,
        remainingRuns,
        trialExpired,
        firstRunDate,
        maxTrialRuns: MAX_TRIAL_RUNS
    };
}

/**
 * Показывает информацию о запусках в консоли при каждом запуске
 */
function showStartupInfo() {
    const status = getActivationStatus();

    if (status.activated) {
        console.log('🔓 Password Manager - Полная версия активирована');
    } else {
        console.log(`⏰ Password Manager - Демоверсия (запуск ${status.trialCount}/${status.maxTrialRuns})`);
        console.log(`📊 Осталось запусков: ${status.remainingRuns}`);

        if (status.trialExpired) {
            console.log('🚫 Пробный период истек! Требуется активация.');
        }

        if (status.firstRunDate) {
            console.log(`📅 Первый запуск: ${status.firstRunDate.toLocaleDateString('ru-RU')}`);
        }
    }
}

// Автоматически показываем информацию при загрузке
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(showStartupInfo, 1000);
});

// Делаем функции глобально доступными для отладки
window.debugActivation = debugActivationStatus;
window.resetActivation = resetActivationData;
window.showStartupInfo = showStartupInfo;
