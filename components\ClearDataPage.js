function ClearDataPage({ onClose }) {
    const [password, setPassword] = React.useState('');
    const [loading, setLoading] = React.useState(false);
    const [error, setError] = React.useState('');
    const [showPassword, setShowPassword] = React.useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            // Хешируем введенный пароль
            const hashedPassword = await hashPassword(password);
            
            // Получаем сохраненный мастер-пароль
            const storedPassword = await getMasterPassword();
            
            // Проверяем совпадение паролей
            if (storedPassword && hashedPassword === storedPassword) {
                // Пароль верный, выполняем очистку
                try {
                    // Показываем уведомление о начале процесса
                    showToast('Очистка базы данных...');
                    
                    // Безопасно очищаем localStorage с сохранением данных активации
                    safeClearLocalStorage();
                    
                    // Устанавливаем флаг для перезагрузки
                    sessionStorage.setItem('dbCleared', 'true');
                    
                    // Удаляем базу данных
                    const request = indexedDB.deleteDatabase('passwordManagerDB');
                    
                    // Принудительно перезагружаем страницу сразу после начала процесса удаления
                    window.location.reload();
                } catch (error) {
                    console.error('Ошибка при очистке данных:', error);
                    showToast('Ошибка при очистке базы данных', 'error');
                }
            } else {
                // Пароль неверный
                setError('Неверный пароль');
            }
        } catch (error) {
            console.error('Error validating password:', error);
            setError('Произошла ошибка при проверке пароля');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="fixed inset-0 bg-gray-900 z-[100]">
            <header className="bg-gray-800 border-b border-gray-700 px-6 py-4">
                <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-3">
                        <i className="fas fa-trash-alt text-2xl text-red-500"></i>
                        <h1 className="text-xl font-bold">Очистка всех данных</h1>
                    </div>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-white transition-colors"
                        title="Закрыть"
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>
            </header>

            <div className="max-w-md mx-auto mt-16 p-6">
                <div className="bg-gray-800 border border-gray-700 rounded-2xl p-6">
                    <div className="text-center mb-6">
                        <i className="fas fa-exclamation-triangle text-5xl text-yellow-400 mb-4"></i>
                        <h2 className="text-xl font-bold">Внимание!</h2>
                        <p className="text-gray-400 mt-4">
                            Вы собираетесь удалить <span className="text-red-500 font-bold">ВСЕ</span> данные из приложения.
                        </p>
                        <p className="text-gray-400 mt-2">
                            Это действие <span className="text-red-500 font-bold">НЕВОЗМОЖНО</span> отменить.
                        </p>
                        <p className="text-gray-400 mt-4">
                            Для подтверждения введите ваш мастер-пароль:
                        </p>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="relative">
                            <input
                                type={showPassword ? "text" : "password"}
                                placeholder="Введите мастер-пароль"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                required
                            />
                            <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                            >
                                <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                            </button>
                        </div>

                        {error && (
                            <div className="text-red-400 text-sm">
                                {error}
                            </div>
                        )}

                        <div className="flex space-x-3 pt-4">
                            <button
                                type="submit"
                                disabled={loading}
                                className="flex-1 py-3 bg-red-600 hover:bg-red-700 rounded-lg font-semibold transition-colors disabled:opacity-50"
                            >
                                {loading ? 'Проверка...' : 'Подтвердить удаление'}
                            </button>
                            <button
                                type="button"
                                onClick={onClose}
                                className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold transition-colors"
                            >
                                Отмена
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
}