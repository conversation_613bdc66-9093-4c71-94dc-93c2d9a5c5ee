function ActivationManager({ children, onLogout, isLoggedIn }) {
    try {
        const [activationState, setActivationState] = React.useState({
            isActivated: false,
            showTrialDialog: false,
            showActivationDialog: false,
            trialCount: 0,
            isTrialExpired: false,
            loading: true,
            hasCheckedAfterLogin: false
        });

        // Проверяем активацию при монтировании (только базовая проверка)
        React.useEffect(() => {
            console.log('🔄 ActivationManager: Initial check...');
            initialCheck();
        }, []);

        // Проверяем активацию ПОСЛЕ входа в систему
        React.useEffect(() => {
            if (isLoggedIn && !activationState.hasCheckedAfterLogin) {
                console.log('🔐 User logged in - checking activation...');
                setTimeout(() => {
                    checkActivationAfterLogin();
                }, 500);
            }
        }, [isLoggedIn, activationState.hasCheckedAfterLogin]);

        const initialCheck = () => {
            try {
                // Только проверяем, активирована ли программа
                const activated = localStorage.getItem('passwordManager_activated') === 'true';
                console.log('✅ Initial check - Is activated:', activated);

                setActivationState({
                    isActivated: activated,
                    showTrialDialog: false,
                    showActivationDialog: false,
                    trialCount: 0,
                    isTrialExpired: false,
                    loading: false,
                    hasCheckedAfterLogin: false
                });

            } catch (error) {
                console.error('❌ Error in initial check:', error);
                setActivationState(prev => ({ ...prev, loading: false }));
            }
        };

        const checkActivationAfterLogin = () => {
            try {
                // Проверяем, активирована ли программа
                const activated = localStorage.getItem('passwordManager_activated') === 'true';
                console.log('🔐 After login check - Is activated:', activated);

                if (activated) {
                    setActivationState(prev => ({
                        ...prev,
                        isActivated: true,
                        showTrialDialog: false,
                        showActivationDialog: false,
                        hasCheckedAfterLogin: true
                    }));
                    return;
                }

                // Увеличиваем счетчик запусков ТОЛЬКО при входе в систему
                const currentCount = parseInt(localStorage.getItem('passwordManager_trialCount') || '0');
                const newCount = currentCount + 1;
                localStorage.setItem('passwordManager_trialCount', newCount.toString());

                // Устанавливаем дату первого запуска
                if (currentCount === 0) {
                    localStorage.setItem('passwordManager_firstRunDate', new Date().toISOString());
                }

                console.log('📊 Trial count after login:', newCount);

                const isExpired = newCount >= 20;
                console.log('⏰ Trial expired:', isExpired);

                setActivationState(prev => ({
                    ...prev,
                    isActivated: false,
                    showTrialDialog: !isExpired,
                    showActivationDialog: isExpired,
                    trialCount: newCount,
                    isTrialExpired: isExpired,
                    hasCheckedAfterLogin: true
                }));

            } catch (error) {
                console.error('❌ Error checking activation after login:', error);
                setActivationState(prev => ({ ...prev, hasCheckedAfterLogin: true }));
            }
        };

        const handleActivation = async (key) => {
            const correctKey = 'neirotopchik_vk_2000_500-7000!!!!!';
            console.log('🔑 Checking activation key:', key);
            console.log('🔑 Correct key:', correctKey);
            console.log('🔑 Keys match:', key === correctKey);

            if (key === correctKey) {
                localStorage.setItem('passwordManager_activated', 'true');
                setActivationState(prev => ({
                    ...prev,
                    isActivated: true,
                    showTrialDialog: false,
                    showActivationDialog: false
                }));
                console.log('🎉 Program activated successfully!');
                return true;
            }
            console.log('❌ Activation failed - incorrect key');
            return false;
        };

        const handleContinueTrial = () => {
            console.log('👤 User chose to continue trial');
            setActivationState(prev => ({
                ...prev,
                showTrialDialog: false
            }));
        };

        const handleCancelActivation = () => {
            console.log('👤 User chose to cancel activation dialog');
            setActivationState(prev => ({
                ...prev,
                showActivationDialog: false
            }));
        };

        if (activationState.loading) {
            return (
                <div className="min-h-screen bg-gray-900 flex items-center justify-center">
                    <div className="text-center">
                        <i className="fas fa-spinner fa-spin text-4xl text-blue-400 mb-4"></i>
                        <p className="text-gray-400">Проверка активации...</p>
                    </div>
                </div>
            );
        }



        return (
            <div>
                {/* Основное приложение */}
                {children}

                {/* Уведомление о демоверсии - показывается ПОВЕРХ всех окон */}
                {activationState.showTrialDialog && isLoggedIn && (
                    <TrialNotification
                        trialCount={activationState.trialCount}
                        remainingRuns={20 - activationState.trialCount}
                        onActivate={() => setActivationState(prev => ({
                            ...prev,
                            showTrialDialog: false,
                            showActivationDialog: true
                        }))}
                        onContinue={handleContinueTrial}
                    />
                )}

                {/* Диалог активации - показывается ПОВЕРХ всех окон */}
                {activationState.showActivationDialog && isLoggedIn && (
                    <ActivationDialog
                        onActivate={handleActivation}
                        onCancel={activationState.isTrialExpired ? () => {} : handleCancelActivation}
                        isTrialExpired={activationState.isTrialExpired}
                        trialCount={activationState.trialCount}
                        remainingRuns={20 - activationState.trialCount}
                    />
                )}
            </div>
        );

    } catch (error) {
        console.error('ActivationManager component error:', error);
        return (
            <div className="min-h-screen bg-gray-900 flex items-center justify-center">
                <div className="text-center">
                    <i className="fas fa-exclamation-triangle text-4xl text-red-400 mb-4"></i>
                    <h2 className="text-xl font-bold mb-2">Ошибка активации</h2>
                    <p className="text-gray-400">Произошла ошибка при проверке активации</p>
                </div>
            </div>
        );
    }
}
