# Руководство по системе активации Password Manager

## 🔑 Ключ активации
```
neirotopchik_vk_2000_500-7000!!!!!
```

## 📋 Как работает система активации

### 1. Демоверсия (10 запусков)
- При каждом запуске показывается уведомление с таймером 10 секунд
- Отображается количество использованных и оставшихся запусков
- Прогресс-бар показывает использование пробного периода
- В Dashboard отображается статус активации

### 2. После 10 запусков
- Принудительное окно активации
- Нельзя продолжить без активации
- Требуется ввод правильного ключа

### 3. После активации
- Программа работает без ограничений
- Уведомления о демоверсии не показываются
- Статус активации сохраняется навсегда

## 🧪 Тестирование системы

### В консоли браузера (F12):

```javascript
// Показать текущий статус
debugActivation()

// Показать информацию о запусках
showStartupInfo()

// Сбросить активацию для тестирования
resetActivation()

// Установить счетчик на 9 (предпоследний запуск)
localStorage.setItem('passwordManager_trialCount', '9')
location.reload()

// Установить счетчик на 10 (принудительная активация)
localStorage.setItem('passwordManager_trialCount', '10')
location.reload()

// Полный сброс для повторного тестирования
resetApp()
```

## 🎯 Особенности реализации

### Таймер запуска (10 секунд)
- Кнопка "Продолжить" заблокирована на 10 секунд
- Обратный отсчет отображается на кнопке
- Можно сразу нажать "Активировать"

### Статус в Dashboard
- Оранжевая полоска с информацией о демоверсии
- Прогресс-бар использования запусков
- Кнопка быстрой активации

### Логирование
- Информация о запусках выводится в консоль
- Подробная диагностика доступна через функции отладки

## 🔧 Устранение неполадок

### Если система активации не работает:
```javascript
// Проверить загрузку функций
typeof isActivated === 'function'

// Полная диагностика
fullAuthDiagnostic()

// Принудительная активация (для тестирования)
localStorage.setItem('passwordManager_activated', 'true')
location.reload()
```

### Если нужно протестировать заново:
```javascript
// Сброс только активации
resetActivation()

// Полный сброс приложения
resetApp()
```

## 📱 Интерфейс

### Уведомление о демоверсии:
- Показывается при каждом запуске (если не активировано)
- Таймер 10 секунд перед разрешением продолжить
- Информация о количестве запусков
- Ссылка на автора для получения ключа

### Диалог активации:
- Поле ввода ключа активации
- Информация о демоверсии
- Ссылка на автора
- Принудительный режим после 10 запусков

### Статус в Dashboard:
- Компактная информация о демоверсии
- Прогресс-бар
- Быстрая активация

## 🎉 После активации
- Все ограничения снимаются
- Уведомления исчезают
- Программа работает в полном режиме
- Статус сохраняется между сессиями
